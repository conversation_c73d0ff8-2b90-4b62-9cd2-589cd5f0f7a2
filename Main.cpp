
#include "Includes/obfuscate.h"
#include "HMOD/Call_Me.h"
#include "Substrate/SubstrateHook.h"
#include <zip.h>
#include <algorithm> // Cho std::min và std::max
#include <chrono>    // Cho đo thời gian
#include <functional> // Cho std::function

#define COL(value) (value / 255)
static float isRed = 0.0f, isGreen = 0.01f, isBlue = 0.0f;
bool setup = false;

// Biến kiểm soát hiển thị menu
static bool showMenu = true;

// Biến để theo dõi việc nhấn 3 ngón tay
static bool threeFingerDetected = false;
static std::chrono::steady_clock::time_point threeFingerStartTime;
static float THREE_FINGER_DURATION = 1.5f; // Thời gian giữ 3 ngón tay (giây) - đ<PERSON> thay đổi từ const thành biến thường
static float lastTouchPositions[10][2] = {0}; // L<PERSON>u vị trí 10 ngón tay
static bool firstTouch = true;
static float MOVEMENT_THRESHOLD = 15.0f; // Ngưỡng di chuyển cho phép (pixels)
static float userHoldDuration = THREE_FINGER_DURATION;
// Biến cho cuộn mượt
static float scrollVelocity = 0.0f;
static float scrollSpeed = 2.5f;
static float deltaTime = 0.0f;
static float lastFrameTime = 0.0f;
static ImVec2 lastTouchPos = ImVec2(0, 0);

// Biến để theo dõi trạng thái cuộn và kéo
static bool isScrolling = false;

// Thêm các biến toàn cục để kiểm soát vị trí menu
static bool isDraggingTitleBar = false;
static ImVec2 menuDragOffset;
static ImVec2 menuPosition;
static bool menuPositionInitialized = false;




// Hàm tính toán khoảng cách di chuyển
float calculateMovement(float oldX, float oldY, float newX, float newY) {
    return sqrt(pow(newX - oldX, 2) + pow(newY - oldY, 2));
}

// Hàm helper để vẽ feature card với thiết kế hiện đại
void DrawFeatureCard(const char* title, std::function<void()> content) {
    extern float isRed, isGreen, isBlue;

    float card_width = ImGui::GetContentRegionAvail().x;
    float start_y = ImGui::GetCursorScreenPos().y;

    // Đo chiều cao nội dung
    ImVec2 content_start = ImGui::GetCursorPos();

    // Header với gradient
    ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
        ImVec2(ImGui::GetCursorScreenPos().x, start_y),
        ImVec2(ImGui::GetCursorScreenPos().x + card_width, start_y + 45),
        ImColor(isRed * 0.3f, isGreen * 0.3f, isBlue * 0.3f, 0.8f),
        ImColor(isRed * 0.5f, isGreen * 0.5f, isBlue * 0.5f, 0.8f),
        ImColor(isRed * 0.4f, isGreen * 0.4f, isBlue * 0.4f, 0.8f),
        ImColor(isRed * 0.2f, isGreen * 0.2f, isBlue * 0.2f, 0.8f)
    );

    // Title
    ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 8);
    ImGui::Indent(15);
    ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]);
    ImGui::TextColored(ImVec4(1.0f, 1.0f, 1.0f, 1.0f), title);
    ImGui::PopFont();
    ImGui::Unindent(15);

    // Content area
    ImGui::SetCursorPosY(ImGui::GetCursorPosY() + 8);
    float content_start_y = ImGui::GetCursorScreenPos().y;

    ImGui::Indent(15);
    content(); // Gọi lambda function
    ImGui::Unindent(15);

    float content_end_y = ImGui::GetCursorScreenPos().y + 15;

    // Background cho content
    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(ImGui::GetCursorScreenPos().x - 15, content_start_y),
        ImVec2(ImGui::GetCursorScreenPos().x + card_width - 15, content_end_y),
        ImColor(0.12f, 0.15f, 0.22f, 0.9f), 8.0f
    );

    // Viền card
    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(ImGui::GetCursorScreenPos().x - 15, start_y),
        ImVec2(ImGui::GetCursorScreenPos().x + card_width - 15, content_end_y),
        ImColor(isRed * 0.7f, isGreen * 0.7f, isBlue * 0.7f, 0.6f), 8.0f, 0, 1.5f
    );

    ImGui::Dummy(ImVec2(0, 15));
}

static size_t WriteCallback(void *ptr, size_t size, size_t nmemb, void *stream) {
    size_t written = fwrite(ptr, size, nmemb, (FILE *)stream);
    return written;
}

bool download_file(std::string url, std::string path) {
    // Tạo thư mục chứa file nếu chưa tồn tại
    std::string directory = path.substr(0, path.find_last_of('/'));
    mkdir(directory.c_str(), 0755);

    curl_global_init(CURL_GLOBAL_ALL);
    bool success = false;

    CURL *curl = curl_easy_init();
    if (curl) {
        FILE *file = fopen(path.c_str(), "wb");
        if (file) {
            curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
            curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
            curl_easy_setopt(curl, CURLOPT_DEFAULT_PROTOCOL, "https");
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, file);
            curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
            curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

            CURLcode result = curl_easy_perform(curl);
            fclose(file);

            success = (result == CURLE_OK);

            if (!success) {
                // Nếu tải thất bại, xóa file để không giữ file hỏng
                remove(path.c_str());
            }
        }
        curl_easy_cleanup(curl);
    }

    curl_global_cleanup();
    return success;
}

// Hàm kiểm tra anti-debugging
void AntiDebuggerCheck() {
    FILE *fp = fopen("/proc/self/status", "r");
    if (fp) {
        char line[256];
        while (fgets(line, sizeof(line), fp)) {
            if (strncmp(line, "TracerPid:", 10) == 0) {
                int tracer_pid = atoi(line + 10);
                if (tracer_pid != 0) {
                    exit(0);
                }
            }
        }
        fclose(fp);
    }
}



bool unZipIcon(std::string file_path) {
    // Kiểm tra xem file có tồn tại không
    struct stat file_stat;
    if (stat(file_path.c_str(), &file_stat) != 0) {
        return false;
    }

    // Tạo thư mục đích
    std::string directoryPath = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/").append("TH/");
    mkdir(directoryPath.c_str(), 0755);

    // Mở file zip
    int error_code = 0;
    zip *archive = zip_open(file_path.c_str(), 0, &error_code);
    if (!archive) {
        return false;
    }

    bool success = true;
    int num_entries = zip_get_num_entries(archive, 0);
    for (int i = 0; i < num_entries; ++i) {
        struct zip_stat file_info;
        if (zip_stat_index(archive, i, 0, &file_info) == 0) {
            // Bỏ qua thư mục
            if (file_info.name[strlen(file_info.name) - 1] == '/') {
                continue;
            }

            // Mở file trong archive
            zip_file *zip_f = zip_fopen_index(archive, i, 0);
            if (zip_f) {
                // Tạo đường dẫn cho file giải nén
                std::string out_path = directoryPath + file_info.name;

                // Tạo các thư mục con nếu cần
                std::string dir_path = out_path.substr(0, out_path.find_last_of('/'));
                for (size_t p = 0; p < dir_path.length(); p++) {
                    if (dir_path[p] == '/' && p > 0) {
                        std::string sub_path = dir_path.substr(0, p);
                        mkdir(sub_path.c_str(), 0755);
                    }
                }
                mkdir(dir_path.c_str(), 0755);

                // Mở file đích để ghi
                FILE *out_file = fopen(out_path.c_str(), "wb");
                if (out_file) {
                    // Cấp phát bộ nhớ cho buffer
                    char buffer[4096];
                    zip_int64_t bytes_read;

                    // Đọc và ghi dữ liệu
                    while ((bytes_read = zip_fread(zip_f, buffer, sizeof(buffer))) > 0) {
                        if (fwrite(buffer, 1, bytes_read, out_file) != (size_t)bytes_read) {
                            success = false;
                            break;
                        }
                    }

                    fclose(out_file);
                } else {
                    success = false;
                }

                zip_fclose(zip_f);
            } else {
                success = false;
            }
        }
    }

    zip_close(archive);

    // Xóa file zip sau khi giải nén thành công
    if (success) {
        if (remove(file_path.c_str()) != 0) {
            // Ghi log lỗi xóa file nếu cần
            // Nhưng vẫn trả về true vì giải nén đã thành công
        }
    }

    return success;
}
// Hàm lưu key vào bộ nhớ - Đã cập nhật với mã hóa và checksum
bool saveKeyToStorage(const char* key) {
    std::string filePath = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/").append("keydata.dat");
    FILE* file = fopen(filePath.c_str(), "wb");
    if (file) {
        // Tạo khóa mã hóa đơn giản từ tên gói
        std::string packageName = GetPack();
        char encryptionKey[16] = {0};
        for (size_t i = 0; i < std::min(packageName.length(), sizeof(encryptionKey)); i++) {
            encryptionKey[i] = packageName[i];
        }

        // Tính checksum
        uint32_t checksum = 0;
        for (size_t i = 0; i < strlen(key); i++) {
            checksum = checksum * 31 + key[i];
        }

        // Lưu checksum
        fwrite(&checksum, sizeof(checksum), 1, file);

        // Mã hóa và lưu key
        size_t keyLen = strlen(key);
        fwrite(&keyLen, sizeof(keyLen), 1, file);
        for (size_t i = 0; i < keyLen; i++) {
            char encryptedChar = key[i] ^ encryptionKey[i % sizeof(encryptionKey)];
            fwrite(&encryptedChar, 1, 1, file);
        }

        fclose(file);
        return true;
    }
    return false;
}

// Hàm đọc key từ bộ nhớ - Đã cập nhật với giải mã và kiểm tra checksum
bool loadKeyFromStorage(char* key, size_t maxSize) {
    std::string filePath = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/").append("keydata.dat");
    FILE* file = fopen(filePath.c_str(), "rb");
    if (file) {
        // Đọc checksum
        uint32_t storedChecksum;
        if (fread(&storedChecksum, sizeof(storedChecksum), 1, file) != 1) {
            fclose(file);
            return false;
        }

        // Đọc độ dài key
        size_t keyLen;
        if (fread(&keyLen, sizeof(keyLen), 1, file) != 1 || keyLen >= maxSize) {
            fclose(file);
            return false;
        }

        // Tạo lại khóa mã hóa
        std::string packageName = GetPack();
        char encryptionKey[16] = {0};
        for (size_t i = 0; i < std::min(packageName.length(), sizeof(encryptionKey)); i++) {
            encryptionKey[i] = packageName[i];
        }

        // Đọc và giải mã key
        for (size_t i = 0; i < keyLen; i++) {
            char encryptedChar;
            if (fread(&encryptedChar, 1, 1, file) != 1) {
                fclose(file);
                return false;
            }
            key[i] = encryptedChar ^ encryptionKey[i % sizeof(encryptionKey)];
        }
        key[keyLen] = '\0';

        // Kiểm tra checksum
        uint32_t calculatedChecksum = 0;
        for (size_t i = 0; i < keyLen; i++) {
            calculatedChecksum = calculatedChecksum * 31 + key[i];
        }

        if (calculatedChecksum != storedChecksum) {
            fclose(file);
            return false;
        }

        fclose(file);
        return true;
    }
    return false;
}

// Hàm lưu cấu hình map
bool SaveMapConfig() {
    // Tạo đường dẫn để lưu cấu hình
    std::string configPath = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/").append("map_config.dat");

    // Mở file để ghi
    FILE* file = fopen(configPath.c_str(), "wb");
    if (file) {
        // Ghi thông số Map Settings
        fwrite(&Config.ESPMenu.MapSize, sizeof(float), 1, file);
        fwrite(&Config.ESPMenu.IconSize, sizeof(float), 1, file);
        fwrite(&Config.ESPMenu.MapOffsetX, sizeof(float), 1, file);
        fwrite(&Config.ESPMenu.MapOffsetY, sizeof(float), 1, file);

        fclose(file);
        return true;
    }
    return false;
}

// Hàm để tải lại cấu hình map khi khởi động
bool LoadMapConfig() {
    // Đường dẫn file cấu hình
    std::string configPath = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/").append("map_config.dat");

    // Kiểm tra file có tồn tại không
    struct stat file_stat;
    if (stat(configPath.c_str(), &file_stat) == 0) {
        // Mở file để đọc
        FILE* file = fopen(configPath.c_str(), "rb");
        if (file) {
            // Đọc thông số Map Settings
            fread(&Config.ESPMenu.MapSize, sizeof(float), 1, file);
            fread(&Config.ESPMenu.IconSize, sizeof(float), 1, file);
            fread(&Config.ESPMenu.MapOffsetX, sizeof(float), 1, file);
            fread(&Config.ESPMenu.MapOffsetY, sizeof(float), 1, file);

            fclose(file);
            return true;
        }
    }
    return false;
}

// Hàm khởi tạo cấu hình ESP
void InitializeESPConfig() {
    // Cấu hình mặc định
    Config.ESPMenu.Enable_ESP = false;
    Config.ESPMenu.MinimapIcon = false;
    Config.ESPMenu.PlayerLine = false;
    Config.ESPMenu.PlayerBox = false;
    Config.ESPMenu.Icon = false;  // Thêm giá trị mặc định cho Icon

    // Cấu hình kích thước bản đồ
    Config.ESPMenu.MapSize = 410.0f;
    Config.ESPMenu.BigMapSize = 410.0f;
    Config.ESPMenu.MapOffsetX = 0.0f;
    Config.ESPMenu.MapOffsetY = 0.0f;
    Config.ESPMenu.IconSize = 18.0f;

    // Màu sắc mặc định
    Config.Color.enemy[0] = 1.0f;   // Red
    Config.Color.enemy[1] = 0.0f;   // Green
    Config.Color.enemy[2] = 0.0f;   // Blue
    Config.Color.enemy[3] = 1.0f;   // Alpha

    Config.Color.team[0] = 0.0f;    // Red
    Config.Color.team[1] = 1.0f;    // Green
    Config.Color.team[2] = 0.0f;    // Blue
    Config.Color.team[3] = 1.0f;    // Alpha

    Config.Color.line[0] = 1.0f;    // Red
    Config.Color.line[1] = 1.0f;    // Green
    Config.Color.line[2] = 1.0f;    // Blue
    Config.Color.line[3] = 0.8f;    // Alpha

    Config.Color.hp_high[0] = 0.0f; // Red
    Config.Color.hp_high[1] = 1.0f; // Green
    Config.Color.hp_high[2] = 0.0f; // Blue
    Config.Color.hp_high[3] = 1.0f; // Alpha

    Config.Color.hp_mid[0] = 1.0f;  // Red
    Config.Color.hp_mid[1] = 1.0f;  // Green
    Config.Color.hp_mid[2] = 0.0f;  // Blue
    Config.Color.hp_mid[3] = 1.0f;  // Alpha

    Config.Color.hp_low[0] = 1.0f;  // Red
    Config.Color.hp_low[1] = 0.0f;  // Green
    Config.Color.hp_low[2] = 0.0f;  // Blue
    Config.Color.hp_low[3] = 1.0f;  // Alpha

    // Cập nhật biến toàn cục để tương thích với code cũ
    Enable_ESP = Config.ESPMenu.Enable_ESP;
    MinimapIcon = Config.ESPMenu.MinimapIcon;
    PlayerLine = Config.ESPMenu.PlayerLine;
    PlayerBox = Config.ESPMenu.PlayerBox;
    Icon = Config.ESPMenu.Icon;

    // Khởi tạo đường dẫn icon
    imagePathBase = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/TH/");
}
#if defined(TOUCH_IL2CPP)

struct UnityEngine_Vector2_Fields {
    float x;
    float y;
};

struct UnityEngine_Vector2_o {
    UnityEngine_Vector2_Fields fields;
};

enum TouchPhase {
    Began = 0,
    Moved = 1,
    Stationary = 2,
    Ended = 3,
    Canceled = 4
};

struct UnityEngine_Touch_Fields {
    int32_t m_FingerId;
    struct UnityEngine_Vector2_o m_Position;
    struct UnityEngine_Vector2_o m_RawPosition;
    struct UnityEngine_Vector2_o m_PositionDelta;
    float m_TimeDelta;
    int32_t m_TapCount;
    int32_t m_Phase;
    int32_t m_Type;
    float m_Pressure;
    float m_maximumPossiblePressure;
    float m_Radius;
    float m_fRadiusVariance;
    float m_AltitudeAngle;
    float m_AzimuthAngle;
};

#endif
bool should_clear_mouse_pos = false;
EGLBoolean (*orig_eglSwapBuffers)(EGLDisplay dpy, EGLSurface surface);
EGLBoolean _eglSwapBuffers(EGLDisplay dpy, EGLSurface surface) {

    eglQuerySurface(dpy, surface, EGL_WIDTH, &glWidth);
    eglQuerySurface(dpy, surface, EGL_HEIGHT, &glHeight);

    if (glWidth <= 0 || glHeight <= 0) {
        return eglSwapBuffers(dpy, surface);
    }
    scaleX = (float) glWidth / screenWidth;
    scaleY = (float) glHeight / screenHeight;

    // Thêm kiểm tra xác thực định kỳ
    static uint32_t frameCount = 0;
    frameCount++;
    if (frameCount % 60 == 0) { // Kiểm tra mỗi 60 frame
        if (!checkAuthIntegrity()) {
            // Phát hiện dấu hiệu bất thường, đã reset auth
            showMenu = false;
        }
        AntiDebuggerCheck();
    }


    if (!setup){
        IMGUI_CHECKVERSION();
        ImGui::CreateContext();
        ImGuiIO& io = ImGui::GetIO();

        // Sử dụng giao diện tối như gốc
        ImGui::StyleColorsDark();

        // Tải font với kích thước lớn hơn để dễ nhìn
        io.Fonts->AddFontFromMemoryTTF(const_cast<std::uint8_t*>(Custom), sizeof(Custom), 30.f, NULL, io.Fonts->GetGlyphRangesVietnamese());
        ImGui_ImplOpenGL3_Init("#version 300 es");

        // Tăng tỷ lệ cho điều khiển cảm ứng tốt hơn
        ImGui::GetStyle().ScaleAllSizes(3.5f);

        // Tùy chỉnh phong cách UI
        ImGuiStyle& style = ImGui::GetStyle();
        style.WindowRounding = 10.0f;
        style.FrameRounding = 6.0f;
        style.ScrollbarRounding = 5.0f;
        style.TabRounding = 6.0f;
        style.GrabRounding = 6.0f;
        style.FramePadding = ImVec2(10, 6);
        style.ItemSpacing = ImVec2(10, 8);
        style.ItemInnerSpacing = ImVec2(8, 6);

        // Cấu hình hỗ trợ cảm ứng tốt hơn
        style.TouchExtraPadding = ImVec2(5.0f, 5.0f);
        style.ScrollbarSize = 12.0f;

        // Vô hiệu hóa thanh cuộn ngang tự động
        style.WindowMenuButtonPosition = ImGuiDir_None;

        // Set custom colors
        style.Colors[ImGuiCol_Button] = ImVec4(0.2f, 0.3f, 0.8f, 0.8f);
        style.Colors[ImGuiCol_ButtonHovered] = ImVec4(0.3f, 0.4f, 0.9f, 0.9f);
        style.Colors[ImGuiCol_ButtonActive] = ImVec4(0.4f, 0.5f, 1.0f, 1.0f);
        style.Colors[ImGuiCol_CheckMark] = ImVec4(0.0f, 0.8f, 0.3f, 1.0f);
        style.Colors[ImGuiCol_SliderGrab] = ImVec4(0.2f, 0.3f, 0.8f, 0.8f);
        style.Colors[ImGuiCol_SliderGrabActive] = ImVec4(0.4f, 0.5f, 1.0f, 1.0f);
        style.Colors[ImGuiCol_Header] = ImVec4(0.2f, 0.3f, 0.8f, 0.7f);
        style.Colors[ImGuiCol_HeaderHovered] = ImVec4(0.3f, 0.4f, 0.9f, 0.8f);
        style.Colors[ImGuiCol_HeaderActive] = ImVec4(0.4f, 0.5f, 1.0f, 1.0f);
        style.Colors[ImGuiCol_Tab] = ImVec4(0.15f, 0.20f, 0.45f, 0.8f);
        style.Colors[ImGuiCol_TabHovered] = ImVec4(0.25f, 0.30f, 0.60f, 0.9f);
        style.Colors[ImGuiCol_TabActive] = ImVec4(0.3f, 0.4f, 0.9f, 1.0f);

        // Điều chỉnh màu và kích thước thanh cuộn
        style.Colors[ImGuiCol_ScrollbarBg] = ImVec4(0.05f, 0.05f, 0.10f, 0.3f);
        style.Colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.3f, 0.4f, 0.9f, 0.8f);
        style.Colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.4f, 0.5f, 1.0f, 0.9f);
        style.Colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.5f, 0.6f, 1.0f, 1.0f);

        // Tải tài nguyên (giữ nguyên phần này)
        std::string file_path = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/").append("icon.zip");

        std::string urlIcon = std::string(OBFUSCATE("https://hmod.io.vn/Imgui-Connect/files/icon.zip"));

        imagePathBase = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/TH/");

        if(!fileExists(file_path)){
            if(download_file(urlIcon, file_path)){
                unZipIcon(file_path);
            }
        }else{
            unZipIcon(file_path);
        }

        setup = true;
    }

    #if defined(TOUCH_IL2CPP)
    ImGuiIO* io = &ImGui::GetIO();
    int (*TouchCount)(void*) = (int (*)(void*)) (IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Input"), OBFUSCATE("get_touchCount"), 0));
    int touchCount = TouchCount(nullptr);

    // Tính deltaTime giữa các khung hình
    float currentTime = ImGui::GetTime();
    deltaTime = currentTime - lastFrameTime;
    lastFrameTime = currentTime;

    // Kiểm tra xem có đúng 3 ngón tay đang chạm màn hình không
    if (touchCount == 3) {
        // Lưu trữ vị trí các ngón tay
        float currentPositions[3][2];
        bool areFingersSteady = true;

        for (int i = 0; i < 3; i++) {
            UnityEngine_Touch_Fields touch = ((UnityEngine_Touch_Fields (*)(int)) (IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Input"), OBFUSCATE("GetTouch"), 1))) (i);

            currentPositions[i][0] = touch.m_Position.fields.x;
            currentPositions[i][1] = touch.m_Position.fields.y;

            // Nếu là lần chạm đầu tiên hoặc chưa phát hiện 3 ngón tay
            if (firstTouch || !threeFingerDetected) {
                lastTouchPositions[i][0] = currentPositions[i][0];
                lastTouchPositions[i][1] = currentPositions[i][1];
                firstTouch = false;
            } else {
                // Tính toán khoảng cách di chuyển
                float movement = calculateMovement(
                    lastTouchPositions[i][0], lastTouchPositions[i][1],
                    currentPositions[i][0], currentPositions[i][1]
                );

                // Nếu di chuyển vượt quá ngưỡng
                if (movement > MOVEMENT_THRESHOLD) {
                    areFingersSteady = false;
                    // Cập nhật vị trí mới
                    lastTouchPositions[i][0] = currentPositions[i][0];
                    lastTouchPositions[i][1] = currentPositions[i][1];
                }
            }
        }

        // Nếu chưa phát hiện 3 ngón tay, bắt đầu đếm thời gian
        if (!threeFingerDetected) {
            threeFingerDetected = true;
            threeFingerStartTime = std::chrono::steady_clock::now();
        } else if (areFingersSteady) {
            // Kiểm tra xem đã giữ 3 ngón tay đủ lâu chưa
            auto currentTime = std::chrono::steady_clock::now();
            float elapsedTime = std::chrono::duration<float>(currentTime - threeFingerStartTime).count();

            // Nếu đã giữ đủ thời gian và các ngón tay ổn định
            if (elapsedTime >= THREE_FINGER_DURATION) {
                showMenu = !showMenu;  // Chuyển đổi trạng thái menu (hiển thị/ẩn)
                threeFingerDetected = false;  // Reset trạng thái
                firstTouch = true; // Reset first touch flag

                std::this_thread::sleep_for(std::chrono::milliseconds(500));
            }
        } else {
            // Nếu ngón tay di chuyển, cập nhật lại thời gian bắt đầu
            threeFingerStartTime = std::chrono::steady_clock::now();
        }
    } else {
        // Reset trạng thái nếu không có đủ 3 ngón tay
        threeFingerDetected = false;
        firstTouch = true;
    }

    // Phương pháp triệt để xử lý cảm ứng
    if (touchCount > 0) {
        UnityEngine_Touch_Fields touch = ((UnityEngine_Touch_Fields (*)(int)) (IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Input"), OBFUSCATE("GetTouch"), 1))) (0);
        float reverseY = io->DisplaySize.y - touch.m_Position.fields.y;
        ImVec2 touchPos = ImVec2(touch.m_Position.fields.x, reverseY);

        // Luôn cập nhật vị trí chuột cho tương tác UI
        io->MousePos = touchPos;

        switch (touch.m_Phase) {
            case TouchPhase::Began:
            {
                io->MouseDown[0] = true;

                // Kiểm tra chạm vào thanh tiêu đề
                if (showMenu) {
                    float titleBarHeight = 40.0f;

                    if (touchPos.x >= menuPosition.x &&
                        touchPos.x <= menuPosition.x + 600 &&
                        touchPos.y >= menuPosition.y &&
                        touchPos.y <= menuPosition.y + titleBarHeight) {
                        isDraggingTitleBar = true;
                        menuDragOffset = ImVec2(touchPos.x - menuPosition.x, touchPos.y - menuPosition.y);
                    } else {
                        isDraggingTitleBar = false;
                    }
                }

                lastTouchPos = touchPos;
                break;
            }

            case TouchPhase::Moved:
            {
                if (isDraggingTitleBar) {
                    // Di chuyển menu theo cách thủ công
                    menuPosition.x = touchPos.x - menuDragOffset.x;
                    menuPosition.y = touchPos.y - menuDragOffset.y;

                    // Giới hạn vị trí trong màn hình
                    menuPosition.x = std::max(0.0f, std::min(menuPosition.x, glWidth - 600.0f));
                    menuPosition.y = std::max(0.0f, std::min(menuPosition.y, glHeight - 100.0f));
                } else {
                    // Xử lý cuộn
                    float deltaY = touchPos.y - lastTouchPos.y;
                    io->MouseWheel += deltaY * 0.01f;
                }

                lastTouchPos = touchPos;
                break;
            }

            case TouchPhase::Ended:
            case TouchPhase::Canceled:
            {
                io->MouseDown[0] = false;
                isDraggingTitleBar = false;
                should_clear_mouse_pos = true;
                break;
            }

            case TouchPhase::Stationary:
            {
                io->MouseDown[0] = true;
                break;
            }

            default:
                break;
        }
    } else {
        io->MouseDown[0] = false;
        isDraggingTitleBar = false;
    }

    #endif
    ImGui_ImplOpenGL3_NewFrame();
    ImGui_ImplAndroid_NewFrame(glWidth, glHeight);
    ImGui::NewFrame();

    // Vẽ ESP bất kể menu đang hiển thị hay không
    DrawESP(ImGui::GetForegroundDrawList());

    // Chỉ hiển thị menu khi biến showMenu = true
    if (showMenu) {
        // Khởi tạo vị trí menu nếu cần
                if (!menuPositionInitialized) {
            // Tính toán kích thước menu dựa trên tỷ lệ màn hình
            float menuWidth = glWidth * 0.5f;     // 3/5 chiều rộng màn hình
            float menuHeight = glHeight * 0.8f;   // 4/5 chiều cao màn hình

            // Vị trí để căn giữa màn hình
            menuPosition = ImVec2(
                (glWidth - menuWidth) / 2,        // Căn giữa theo chiều ngang
                (glHeight - menuHeight) / 2       // Căn giữa theo chiều dọc
            );

            menuPositionInitialized = true;
        }
        // Hiệu ứng chuyển màu RGB như cũ
        auto isFrames = ImGui::GetFrameCount();
        if(isFrames % 1 == 0) {
            if(isGreen == 0.01f && isBlue == 0.0f){
                isRed += 0.01f;
                 }

            if(isRed > 0.99f && isBlue == 0.0f){
                isRed = 1.0f;
                isGreen += 0.01f;
                }

            if(isGreen > 0.99f && isBlue == 0.0f){
                isGreen = 1.0f;
                isRed -= 0.01f;
            }

           if(isRed < 0.01f && isGreen == 1.0f){
                isRed = 0.0f;
                isBlue += 0.01f;
                }

            if(isBlue > 0.99f && isRed == 0.0f){
                isBlue = 1.0f;
                isGreen -= 0.01f;
                }
            if(isGreen < 0.01f && isBlue == 1.0f){
                isGreen = 0.0f;
                isRed += 0.01f;
                }
            if(isRed > 0.99f && isGreen == 0.0f){
                isRed = 1.0f;
                isBlue -= 0.01f;
                }
            if(isBlue < 0.01f && isGreen == 0.0f){
                isBlue = 0.0f;
                isRed -= 0.01f;
                if(isRed < 0.01f)
                isGreen = 0.01f;
            }
        }

        // Đặt vị trí menu tùy chỉnh
        ImGui::SetNextWindowPos(menuPosition, ImGuiCond_Always);
        ImGui::SetNextWindowSize(ImVec2(glWidth * 0.5f, glHeight * 0.8f), ImGuiCond_FirstUseEver);

       ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 3.0f);
        ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.07f, 0.07f, 0.12f, 0.95f));
        ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(isRed, isGreen, isBlue, 0.8f));

        // TẮT chức năng di chuyển của ImGui
        ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoMove;

        bool open = true;
        if (ImGui::Begin(OBFUSCATE("LIÊN QUÂN MOBILE 1.57.1.8 - HMOD.INFO"), &open, window_flags)) {

            // Gọi kiểm tra tính toàn vẹn định kỳ
            if (!checkAuthIntegrity()) {
                // Nếu phát hiện dấu hiệu bất thường, đã reset auth ở trên
                showMenu = false;
                ImGui::End();
                ImGui::PopStyleColor(2);
                ImGui::PopStyleVar();
                return orig_eglSwapBuffers(dpy, surface);
            }

            static bool LoginHack = false;
            static std::string err;

            if (!LoginHack) {
                ImGui::Text(OBFUSCATE(" Login! ( Copy Key Trước Khi Mở Game )"));

                // Khai báo biến để lưu key
                static char s[150] = {0};
                static bool keyLoaded = false;

                // Thử đọc key đã lưu khi lần đầu hiển thị màn hình đăng nhập
                if (!keyLoaded) {
                    keyLoaded = true;
                    if (loadKeyFromStorage(s, sizeof(s))) {
                        // Tự động đăng nhập với key đã lưu
                        err = Login(s);
                        if (err == "OK") {
                            LoginHack = isAuthenticated(); // Sử dụng hàm kiểm tra mới
                        }
                    }
                }

                ImGui::PushItemWidth(-1);
                ImGui::InputText("##key", s, sizeof s);

                ImGui::PopItemWidth();
                ImGui::PushItemWidth(-1);
                if (ImGui::Button(OBFUSCATE(" Paste Key  "), ImVec2(ImGui::GetWindowContentRegionWidth(), 0))) {
                    auto key = getClipboard();
                    strncpy(s, key.c_str(), sizeof s);
                }

                ImGui::PopItemWidth();

                ImGui::PushItemWidth(-1);

                if (ImGui::Button(OBFUSCATE("Login"), ImVec2(ImGui::GetWindowContentRegionWidth(), 0))) {
                    err = Login(s);
                    if (err == "OK") {
                        LoginHack = isAuthenticated(); // Sử dụng hàm kiểm tra mới

                        // Lưu key nếu đăng nhập thành công
                        if (isAuthenticated()) {
                            saveKeyToStorage(s);
                        }
                    }
                }

                ImGui::PopItemWidth();

                if (!err.empty() && err != std::string(OBFUSCATE("OK"))) {
                    ImGui::Text(OBFUSCATE("Lỗi : %s"), err.c_str());
                }

                // Hiển thị thông tin hết hạn nếu đăng nhập thành công
                if (isAuthenticated()) {
                    ImGui::TextColored(ImVec4(COL(0), COL(255), COL(0), COL(255)), OBFUSCATE("Ngày Hết Hạn: %s"), EXP.c_str());
                }
            } else {
                // Thêm kiểm tra xác thực định kỳ
                if (!isAuthenticated()) {
                    LoginHack = false;
                    showMenu = false;
                    ImGui::End();
                    ImGui::PopStyleColor(2);
                    ImGui::PopStyleVar();
                    return orig_eglSwapBuffers(dpy, surface);
                }

                ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]);
                ImGui::TextColored(ImVec4(isRed, isGreen, isBlue, 1.0f), OBFUSCATE("Arena of Valor Extrasensory Tool"));
                ImGui::PopFont();

                ImGui::Spacing();
                // Nice separator with gradient
                float window_width = ImGui::GetWindowWidth() - 20.0f;
                ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
                    ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
                    ImVec2(ImGui::GetCursorScreenPos().x + window_width, ImGui::GetCursorScreenPos().y + 4),
                    ImColor(isRed, isGreen, isBlue, 1.0f),
                    ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 1.0f),
                    ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 1.0f),
                    ImColor(isRed, isGreen, isBlue, 1.0f)
                );
                ImGui::Dummy(ImVec2(0, 8));

                // Kiểm tra token xác thực
                if (isAuthenticated()) {
                    // Thiết kế TabBar mới - gọn gàng và đẹp mắt
                    ImGuiTabBarFlags tabbar_flags = ImGuiTabBarFlags_FittingPolicyScroll;

                    if (ImGui::BeginTabBar("##MainTabBar", tabbar_flags)) {
                        // Style cho tabs - hiện đại và dễ nhìn
                        ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(20, 12));
                        ImGui::PushStyleVar(ImGuiStyleVar_TabRounding, 8.0f);

                        // Màu sắc gradient cho tabs
                        ImGui::PushStyleColor(ImGuiCol_TabActive, ImVec4(isRed, isGreen, isBlue, 0.9f));
                        ImGui::PushStyleColor(ImGuiCol_TabHovered, ImVec4(isRed * 0.8f, isGreen * 0.8f, isBlue * 0.8f, 0.8f));
                        ImGui::PushStyleColor(ImGuiCol_Tab, ImVec4(0.12f, 0.15f, 0.25f, 0.8f));
                        ImGui::PushStyleColor(ImGuiCol_TabUnfocused, ImVec4(0.08f, 0.10f, 0.18f, 0.6f));
                        ImGui::PushStyleColor(ImGuiCol_TabUnfocusedActive, ImVec4(0.15f, 0.18f, 0.28f, 0.8f));




                        // ===== TAB CHÍNH - THIẾT KẾ MỚI =====
                        if (ImGui::BeginTabItem(OBFUSCATE("🎯 ESP Features"))) {
                            ImGui::PopStyleVar(2);  // Pop FramePadding và TabRounding

                            // Container chính với thiết kế hiện đại
                            float content_width = ImGui::GetContentRegionAvail().x;
                            float available_height = ImGui::GetContentRegionAvail().y - 15;

                            // Background gradient cho tab
                            ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
                                ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
                                ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + available_height),
                                ImColor(0.06f, 0.08f, 0.12f, 0.95f),
                                ImColor(0.08f, 0.10f, 0.15f, 0.95f),
                                ImColor(0.10f, 0.12f, 0.18f, 0.95f),
                                ImColor(0.08f, 0.10f, 0.15f, 0.95f)
                            );

                            // Viền gradient
                            ImGui::GetWindowDrawList()->AddRect(
                                ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
                                ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + available_height),
                                ImColor(isRed, isGreen, isBlue, 0.6f), 12.0f, 0, 2.0f
                            );

                            ImGui::Dummy(ImVec2(0, 10));
                            ImGui::Indent(15);

                            // Scroll region với style mới
                            ImGui::BeginChild("MainFeatures", ImVec2(content_width - 30, available_height - 25), false, ImGuiWindowFlags_NoScrollbar);

                            // ===== CARD 1: ESP MASTER CONTROL =====
                            DrawFeatureCard("ESP Master Control", [&]() {
                                // Toggle chính ESP với thiết kế đặc biệt
                                ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(15, 10));
                                ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0.2f, 1.0f, 0.4f, 1.0f));
                                ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.15f, 0.15f, 0.25f, 0.9f));
                                ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.2f, 0.2f, 0.35f, 1.0f));
                                ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(isRed, isGreen, isBlue, 0.8f));
                                ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 2.0f);

                                bool old_esp = Enable_ESP;
                                ImGui::Checkbox(OBFUSCATE("🔥 Kích hoạt ESP System"), &Config.ESPMenu.Enable_ESP);

                                if (old_esp != Config.ESPMenu.Enable_ESP) {
                                    // Hiệu ứng glow khi toggle
                                    ImGui::GetWindowDrawList()->AddRectFilled(
                                        ImVec2(ImGui::GetItemRectMin().x - 8, ImGui::GetItemRectMin().y - 8),
                                        ImVec2(ImGui::GetItemRectMax().x + 8, ImGui::GetItemRectMax().y + 8),
                                        ImColor(isRed, isGreen, isBlue, 0.3f), 12.0f
                                    );
                                    Enable_ESP = Config.ESPMenu.Enable_ESP;
                                }

                                ImGui::PopStyleVar(2);
                                ImGui::PopStyleColor(4);

                                // Status indicator
                                ImGui::SameLine();
                                ImGui::TextColored(
                                    Config.ESPMenu.Enable_ESP ? ImVec4(0.2f, 1.0f, 0.4f, 1.0f) : ImVec4(0.8f, 0.3f, 0.3f, 1.0f),
                                    Config.ESPMenu.Enable_ESP ? "● ACTIVE" : "● INACTIVE"
                                );
                            });

                            // ===== CARD 2: CAMERA CONTROL =====
                            DrawFeatureCard("🎥 Camera Height Control", [&]() {
                                ImGui::Text("Điều chỉnh độ cao camera để có tầm nhìn tốt hơn");
                                ImGui::Spacing();

                                // Style cho slider
                                ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.15f, 0.15f, 0.25f, 0.8f));
                                ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.2f, 0.2f, 0.3f, 0.8f));
                                ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(isRed, isGreen, isBlue, 0.9f));
                                ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(isRed + 0.2f, isGreen + 0.2f, isBlue + 0.2f, 1.0f));

                                ImGui::SliderInt("##CameraHeight", &CameraHeight, 0, 10, "Level: %d");

                                // Nút điều chỉnh nhanh
                                ImGui::SameLine();
                                if (ImGui::Button("−", ImVec2(35, 35))) {
                                    if (CameraHeight > 0) CameraHeight--;
                                }
                                ImGui::SameLine();
                                if (ImGui::Button("+", ImVec2(35, 35))) {
                                    if (CameraHeight < 10) CameraHeight++;
                                }

                                ImGui::PopStyleColor(4);
                            });

                            // ===== CARD 3: ESP FEATURES =====
                            DrawFeatureCard("🎯 ESP Features", [&]() {
                                // Grid layout cho các checkbox
                                float item_width = (ImGui::GetContentRegionAvail().x - 20) / 2.0f;

                                ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(10, 8));
                                ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0.2f, 1.0f, 0.4f, 1.0f));
                                ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.15f, 0.15f, 0.25f, 0.8f));
                                ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.2f, 0.2f, 0.35f, 1.0f));
                                ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(isRed * 0.7f, isGreen * 0.7f, isBlue * 0.7f, 0.8f));
                                ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 1.5f);

                                // Hàng 1
                                ImGui::BeginGroup();
                                ImGui::SetNextItemWidth(item_width);
                                if (ImGui::Checkbox(OBFUSCATE("📍 Mini Map Icons"), &Config.ESPMenu.MinimapIcon)) {
                                    MinimapIcon = Config.ESPMenu.MinimapIcon;
                                }
                                ImGui::EndGroup();

                                ImGui::SameLine();
                                ImGui::BeginGroup();
                                ImGui::SetNextItemWidth(item_width);
                                if (ImGui::Checkbox(OBFUSCATE("📏 Player Lines"), &Config.ESPMenu.PlayerLine)) {
                                    PlayerLine = Config.ESPMenu.PlayerLine;
                                }
                                ImGui::EndGroup();

                                ImGui::Spacing();

                                // Hàng 2
                                ImGui::BeginGroup();
                                ImGui::SetNextItemWidth(item_width);
                                if (ImGui::Checkbox(OBFUSCATE("🗺️ Big Map Icons"), &Config.ESPMenu.Icon)) {
                                    Icon = Config.ESPMenu.Icon;
                                }
                                ImGui::EndGroup();

                                ImGui::SameLine();
                                ImGui::BeginGroup();
                                ImGui::SetNextItemWidth(item_width);
                                if (ImGui::Checkbox(OBFUSCATE("📦 Player Boxes"), &Config.ESPMenu.PlayerBox)) {
                                    PlayerBox = Config.ESPMenu.PlayerBox;
                                }
                                ImGui::EndGroup();

                                ImGui::PopStyleVar(2);
                                ImGui::PopStyleColor(4);
                            });

                            ImGui::EndChild();  // End MainFeatures
                            ImGui::Unindent(15);
                            ImGui::EndTabItem();
                        } else {
                            ImGui::PopStyleVar(2);  // Pop FramePadding và TabRounding nếu tab không được chọn
                        }
                        // ===== TAB MAP SETTINGS - THIẾT KẾ MỚI =====
                        if (ImGui::BeginTabItem(OBFUSCATE("⚙️ Map Settings"))) {
                            ImGui::PopStyleVar(2);  // Pop FramePadding và TabRounding

                            float content_width = ImGui::GetContentRegionAvail().x;
                            float available_height = ImGui::GetContentRegionAvail().y - 15;

                            // Background gradient
                            ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
                                ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
                                ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + available_height),
                                ImColor(0.06f, 0.08f, 0.12f, 0.95f),
                                ImColor(0.08f, 0.10f, 0.15f, 0.95f),
                                ImColor(0.10f, 0.12f, 0.18f, 0.95f),
                                ImColor(0.08f, 0.10f, 0.15f, 0.95f)
                            );

                            ImGui::GetWindowDrawList()->AddRect(
                                ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
                                ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + available_height),
                                ImColor(isRed, isGreen, isBlue, 0.6f), 12.0f, 0, 2.0f
                            );

                            ImGui::Dummy(ImVec2(0, 10));
                            ImGui::Indent(15);

                            ImGui::BeginChild("MapSettings", ImVec2(content_width - 30, available_height - 25), false, ImGuiWindowFlags_NoScrollbar);

                            // ===== CARD 1: MINIMAP SETTINGS =====
                            DrawFeatureCard("🗺️ Minimap Configuration", [&]() {
                                ImGui::Text("Điều chỉnh kích thước và vị trí minimap");
                                ImGui::Spacing();

                                // Style cho controls
                                ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.15f, 0.15f, 0.25f, 0.8f));
                                ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.2f, 0.2f, 0.3f, 0.8f));
                                ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(isRed, isGreen, isBlue, 0.9f));
                                ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(isRed + 0.2f, isGreen + 0.2f, isBlue + 0.2f, 1.0f));
                                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(isRed * 0.7f, isGreen * 0.7f, isBlue * 0.7f, 0.8f));
                                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(isRed, isGreen, isBlue, 0.9f));

                                // Map Size
                                ImGui::Text("Map Scale:");
                                ImGui::SliderFloat("##MapScale", &Config.ESPMenu.MapSize, 100.0f, 800.0f, "%.0f");
                                ImGui::SameLine();
                                if (ImGui::Button("−##MapSize", ImVec2(30, 30))) {
                                    Config.ESPMenu.MapSize = std::max(100.0f, Config.ESPMenu.MapSize - 5.0f);
                                }
                                ImGui::SameLine();
                                if (ImGui::Button("+##MapSize", ImVec2(30, 30))) {
                                    Config.ESPMenu.MapSize = std::min(800.0f, Config.ESPMenu.MapSize + 5.0f);
                                }

                                ImGui::Spacing();

                                // Icon Size
                                ImGui::Text("Icon Size:");
                                ImGui::SliderFloat("##IconSize", &Config.ESPMenu.IconSize, 5.0f, 40.0f, "%.1f");
                                ImGui::SameLine();
                                if (ImGui::Button("−##IconSize", ImVec2(30, 30))) {
                                    Config.ESPMenu.IconSize = std::max(5.0f, Config.ESPMenu.IconSize - 1.0f);
                                }
                                ImGui::SameLine();
                                if (ImGui::Button("+##IconSize", ImVec2(30, 30))) {
                                    Config.ESPMenu.IconSize = std::min(40.0f, Config.ESPMenu.IconSize + 1.0f);
                                }

                                ImGui::PopStyleColor(6);
                            });

                            // ===== CARD 2: MAP OFFSET SETTINGS =====
                            DrawFeatureCard("📍 Map Position Adjustment", [&]() {
                                ImGui::Text("Điều chỉnh vị trí map nếu bị lệch");
                                ImGui::Spacing();

                                ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.15f, 0.15f, 0.25f, 0.8f));
                                ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.2f, 0.2f, 0.3f, 0.8f));
                                ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(isRed, isGreen, isBlue, 0.9f));
                                ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(isRed + 0.2f, isGreen + 0.2f, isBlue + 0.2f, 1.0f));
                                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(isRed * 0.7f, isGreen * 0.7f, isBlue * 0.7f, 0.8f));
                                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(isRed, isGreen, isBlue, 0.9f));

                                // Offset X
                                ImGui::Text("Horizontal Offset:");
                                ImGui::SliderFloat("##OffsetX", &Config.ESPMenu.MapOffsetX, -200.0f, 200.0f, "%.1f");
                                ImGui::SameLine();
                                if (ImGui::Button("←##OffsetX", ImVec2(30, 30))) {
                                    Config.ESPMenu.MapOffsetX = std::max(-200.0f, Config.ESPMenu.MapOffsetX - 5.0f);
                                }
                                ImGui::SameLine();
                                if (ImGui::Button("→##OffsetX", ImVec2(30, 30))) {
                                    Config.ESPMenu.MapOffsetX = std::min(200.0f, Config.ESPMenu.MapOffsetX + 5.0f);
                                }

                                ImGui::Spacing();

                                // Offset Y
                                ImGui::Text("Vertical Offset:");
                                ImGui::SliderFloat("##OffsetY", &Config.ESPMenu.MapOffsetY, -200.0f, 200.0f, "%.1f");
                                ImGui::SameLine();
                                if (ImGui::Button("↑##OffsetY", ImVec2(30, 30))) {
                                    Config.ESPMenu.MapOffsetY = std::max(-200.0f, Config.ESPMenu.MapOffsetY - 5.0f);
                                }
                                ImGui::SameLine();
                                if (ImGui::Button("↓##OffsetY", ImVec2(30, 30))) {
                                    Config.ESPMenu.MapOffsetY = std::min(200.0f, Config.ESPMenu.MapOffsetY + 5.0f);
                                }

                                ImGui::PopStyleColor(6);
                            });

                            // ===== CARD 3: SAVE CONFIGURATION =====
                            DrawFeatureCard("💾 Save Settings", [&]() {
                                static bool showSaveMessage = false;
                                static float saveMessageTimer = 0.0f;

                                ImGui::Text("Lưu tất cả cài đặt map hiện tại");
                                ImGui::Spacing();

                                // Style cho nút save
                                ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(isRed * 0.8f, isGreen * 0.8f, isBlue * 0.8f, 0.9f));
                                ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(isRed, isGreen, isBlue, 1.0f));
                                ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(isRed * 1.2f, isGreen * 1.2f, isBlue * 1.2f, 1.0f));

                                float button_width = ImGui::GetContentRegionAvail().x * 0.7f;
                                ImGui::SetCursorPosX((ImGui::GetContentRegionAvail().x - button_width) * 0.5f);

                                if (ImGui::Button("💾 Save Configuration", ImVec2(button_width, 40))) {
                                    if (SaveMapConfig()) {
                                        showSaveMessage = true;
                                        saveMessageTimer = 2.0f;
                                    }
                                }

                                ImGui::PopStyleColor(3);

                                // Success message
                                if (showSaveMessage) {
                                    ImGui::Spacing();
                                    ImGui::SetCursorPosX((ImGui::GetContentRegionAvail().x - ImGui::CalcTextSize("✅ Configuration saved successfully!").x) * 0.5f);
                                    ImGui::TextColored(ImVec4(0.2f, 1.0f, 0.4f, 1.0f), "✅ Configuration saved successfully!");
                                    saveMessageTimer -= ImGui::GetIO().DeltaTime;
                                    if (saveMessageTimer <= 0.0f) {
                                        showSaveMessage = false;
                                    }
                                }
                            });

                            ImGui::EndChild();  // End MapSettings
                            ImGui::Unindent(15);
                            ImGui::EndTabItem();
                        } else {
                            ImGui::PopStyleVar(2);  // Pop FramePadding và TabRounding nếu tab không được chọn
                        }

                        // ===== TAB INFORMATION - THIẾT KẾ MỚI =====
                        if (ImGui::BeginTabItem(OBFUSCATE("ℹ️ Information"))) {
                            ImGui::PopStyleVar(2);  // Pop FramePadding và TabRounding

                            float content_width = ImGui::GetContentRegionAvail().x;
                            float available_height = ImGui::GetContentRegionAvail().y - 15;

                            // Background gradient
                            ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
                                ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
                                ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + available_height),
                                ImColor(0.06f, 0.08f, 0.12f, 0.95f),
                                ImColor(0.08f, 0.10f, 0.15f, 0.95f),
                                ImColor(0.10f, 0.12f, 0.18f, 0.95f),
                                ImColor(0.08f, 0.10f, 0.15f, 0.95f)
                            );

                            ImGui::GetWindowDrawList()->AddRect(
                                ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
                                ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + available_height),
                                ImColor(isRed, isGreen, isBlue, 0.6f), 12.0f, 0, 2.0f
                            );

                            ImGui::Dummy(ImVec2(0, 10));
                            ImGui::Indent(15);

                            ImGui::BeginChild("Information", ImVec2(content_width - 30, available_height - 25), false, ImGuiWindowFlags_NoScrollbar);

                            // ===== CARD 1: ACCOUNT INFORMATION =====
                            DrawFeatureCard("👤 Account Information", [&]() {
                                // Expiry date với icon
                                ImGui::Text("📅 Expiry Date:");
                                ImGui::SameLine();
                                ImGui::TextColored(ImVec4(0.2f, 1.0f, 0.4f, 1.0f), "%s", EXP.c_str());

                                ImGui::Spacing();

                                // Antiban status với icon
                                ImGui::Text("🛡️ Antiban Status:");
                                ImGui::SameLine();
                                if (!Bypass) {
                                    ImGui::TextColored(ImVec4(1.0f, 0.3f, 0.3f, 1.0f), "❌ Deactivated");
                                } else {
                                    ImGui::TextColored(ImVec4(0.2f, 1.0f, 0.4f, 1.0f), "✅ Activated");
                                }

                                ImGui::Spacing();

                                // Seller info
                                if (hovaten.length() > 0) {
                                    ImGui::Text("👨‍💼 Seller:");
                                    ImGui::SameLine();
                                    ImGui::TextColored(ImVec4(isRed, isGreen, isBlue, 1.0f), "%s", hovaten.c_str());
                                }
                            });

                            // ===== CARD 2: VERSION INFORMATION =====
                            DrawFeatureCard("📋 Version Information", [&]() {
                                ImGui::Text("🔧 Version Details:");
                                ImGui::Spacing();

                                if (thongbao.length() > 0) {
                                    ImGui::TextWrapped("%s", thongbao.c_str());
                                } else {
                                    ImGui::TextColored(ImVec4(0.8f, 0.8f, 0.8f, 1.0f), "Version: Unknown");
                                }
                            });

                            // Kiểm tra hạn sử dụng thông qua hệ thống xác thực mới
                            if (!isAuthenticated() || time(0) > g_AuthExpireTime) {
                                resetAuth();
                                LoginHack = false;
                                ImGui_ImplAndroid_Shutdown();
                                ImGui_ImplOpenGL3_Shutdown();
                                ImGui::DestroyContext();
                            }

                            ImGui::EndChild();  // End Information
                            ImGui::Unindent(15);
                            ImGui::EndTabItem();
                        } else {
                            ImGui::PopStyleVar(2);  // Pop FramePadding và TabRounding nếu tab không được chọn
                        }
                        // ===== TAB SETTINGS - THIẾT KẾ MỚI =====
                        if (ImGui::BeginTabItem(OBFUSCATE("⚙️ Settings"))) {
                            ImGui::PopStyleVar(2);  // Pop FramePadding và TabRounding

                            float content_width = ImGui::GetContentRegionAvail().x;
                            float available_height = ImGui::GetContentRegionAvail().y - 15;

                            // Background gradient
                            ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
                                ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
                                ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + available_height),
                                ImColor(0.06f, 0.08f, 0.12f, 0.95f),
                                ImColor(0.08f, 0.10f, 0.15f, 0.95f),
                                ImColor(0.10f, 0.12f, 0.18f, 0.95f),
                                ImColor(0.08f, 0.10f, 0.15f, 0.95f)
                            );

                            ImGui::GetWindowDrawList()->AddRect(
                                ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
                                ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + available_height),
                                ImColor(isRed, isGreen, isBlue, 0.6f), 12.0f, 0, 2.0f
                            );

                            ImGui::Dummy(ImVec2(0, 10));
                            ImGui::Indent(15);

                            ImGui::BeginChild("Settings", ImVec2(content_width - 30, available_height - 25), false, ImGuiWindowFlags_NoScrollbar);

                            // ===== CARD 1: MENU TRIGGER SETTINGS =====
                            DrawFeatureCard("🎮 Menu Control", [&]() {
                                // Status hiện tại
                                ImGui::Text("📱 Current Status:");
                                ImGui::SameLine();
                                ImGui::TextColored(
                                    showMenu ? ImVec4(0.2f, 1.0f, 0.4f, 1.0f) : ImVec4(1.0f, 0.3f, 0.3f, 1.0f),
                                    showMenu ? "👁️ Visible" : "🙈 Hidden"
                                );

                                ImGui::Spacing();
                                ImGui::TextWrapped("💡 Tip: Place 3 fingers on screen and hold steady for %.1f seconds to toggle menu", THREE_FINGER_DURATION);
                            });

                            // ===== CARD 2: GESTURE SETTINGS =====
                            DrawFeatureCard("✋ Gesture Settings", [&]() {
                                ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.15f, 0.15f, 0.25f, 0.8f));
                                ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.2f, 0.2f, 0.3f, 0.8f));
                                ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(isRed, isGreen, isBlue, 0.9f));
                                ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(isRed + 0.2f, isGreen + 0.2f, isBlue + 0.2f, 1.0f));

                                // Hold Duration
                                ImGui::Text("⏱️ Hold Duration:");
                                ImGui::SliderFloat("##HoldDuration", &userHoldDuration, 0.5f, 3.0f, "%.1f seconds");
                                THREE_FINGER_DURATION = userHoldDuration;

                                ImGui::Spacing();

                                // Touch Sensitivity
                                ImGui::Text("🎯 Touch Sensitivity:");
                                ImGui::SliderFloat("##TouchSensitivity", &MOVEMENT_THRESHOLD, 5.0f, 30.0f, "%.1f pixels");
                                ImGui::TextColored(ImVec4(0.7f, 0.7f, 0.7f, 1.0f), "Lower = More sensitive | Higher = More stable");

                                ImGui::PopStyleColor(4);
                            });

                            ImGui::EndChild();  // End Settings
                            ImGui::Unindent(15);
                            ImGui::EndTabItem();
                        } else {
                            ImGui::PopStyleVar(2);  // Pop FramePadding và TabRounding nếu tab không được chọn
                        }

                        // Kết thúc TabBar và pop styles
                        ImGui::PopStyleColor(5); // Pop tab colors
                        ImGui::EndTabBar();
                    }

                    }
                }
            }
        }

        // Xử lý khi nhấn nút đóng (dấu X)
        if (!open) {
            showMenu = false; // Ẩn hoàn toàn menu thay vì chỉ thu gọn
        }

        ImGui::End();
        ImGui::PopStyleColor(2);
        ImGui::PopStyleVar();
    }

    ImGui::Render();
    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

    if (should_clear_mouse_pos) {
        ImGui::GetIO().MousePos = ImVec2(-1, -1);
        should_clear_mouse_pos = false;
    }

    return orig_eglSwapBuffers(dpy, surface);
}

void *Init_Thread(void *) {
    AntiDebuggerCheck();
   espManager = new ESPManager();
   ActorLinker_enemy = new ESPManager();
   pthread_t antiDebugThread;
   pthread_create(&antiDebugThread, NULL, [](void*) -> void* {
       while (true) {
           AntiDebuggerCheck();
           sleep(5); // Kiểm tra mỗi 5 giây
       }
       return nullptr;
   }, NULL);

   InitializeESPConfig(); // Khởi tạo cấu hình ESP
   LoadMapConfig();
   KittyMemory::ProcMap Il2cppMap, AnogsMap;
   while (!Il2cppMap.isValid() && !AnogsMap.isValid()) {
       Il2cppMap = KittyMemory::getLibraryMap("libil2cpp.so");
       AnogsMap = KittyMemory::getLibraryMap("libanogs.so");
       Bypass = true;
       sleep(1);
   }
   IL2Cpp::Il2CppAttach();

Tools::Hook((void *) DobbySymbolResolver(OBFUSCATE("/system/lib/libEGL.so"), OBFUSCATE("eglSwapBuffers")), (void *) _eglSwapBuffers, (void **) &orig_eglSwapBuffers);
#if defined(__aarch64__)
MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CLoginSystem"), OBFUSCATE("_LoginSuccess"), 1),"C0035FD6").Modify();

 MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CGCloudUpdateSystem"), OBFUSCATE("get_IsAutoLogin"), 0),"C0035FD6").Modify();

#else
MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CLoginSystem"), OBFUSCATE("_LoginSuccess"), 1),"1EFF2FE1").Modify();

 MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CGCloudUpdateSystem"), OBFUSCATE("get_IsAutoLogin"), 0),"1EFF2FE1").Modify();

#endif

while (!isAuthenticated()) {
        sleep(1);
    }

    // Sau khi đăng nhập thành công thì mới áp dụng các hook khác
    if (isAuthenticated()) {

#include "Hooks.h"
  }
    return nullptr;
}



JNIEXPORT jint JNICALL
JNI_OnLoad(JavaVM *vm, void * reserved) {
    jvm = vm;
    JNIEnv *env;

    AntiDebuggerCheck();
    vm->GetEnv((void **) &env, JNI_VERSION_1_6);

    Tools::Hook((void *) DobbySymbolResolver(OBFUSCATE("/system/lib/libandroid.so"), OBFUSCATE("ANativeWindow_getWidth")), (void *) _ANativeWindow_getWidth, (void **) &orig_ANativeWindow_getWidth);
    Tools::Hook((void *) DobbySymbolResolver(OBFUSCATE("/system/lib/libandroid.so"), OBFUSCATE("ANativeWindow_getHeight")), (void *) _ANativeWindow_getHeight, (void **) &orig_ANativeWindow_getHeight);

    pthread_t myThread;
    pthread_create(&myThread, NULL, Init_Thread, NULL);

    return JNI_VERSION_1_6;
}


