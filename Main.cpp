
#include "Includes/obfuscate.h"
#include "HMOD/Call_Me.h"
#include "Substrate/SubstrateHook.h"
#include <zip.h>
#include <algorithm> // Cho std::min và std::max
#include <chrono>    // Cho đo thời gian
#include <functional> // Cho std::function

#define COL(value) (value / 255)
static float isRed = 0.0f, isGreen = 0.01f, isBlue = 0.0f;
bool setup = false;

// Biến kiểm soát hiển thị menu
static bool showMenu = true;

// Biến để theo dõi việc nhấn 3 ngón tay
static bool threeFingerDetected = false;
static std::chrono::steady_clock::time_point threeFingerStartTime;
static float THREE_FINGER_DURATION = 1.5f; // Thời gian giữ 3 ngón tay (giây) - đ<PERSON> thay đổi từ const thành biến thường
static float lastTouchPositions[10][2] = {0}; // L<PERSON>u vị trí 10 ngón tay
static bool firstTouch = true;
static float MOVEMENT_THRESHOLD = 15.0f; // Ngưỡng di chuyển cho phép (pixels)
static float userHoldDuration = THREE_FINGER_DURATION;
// Biến cho cuộn mượt
static float scrollVelocity = 0.0f;
static float scrollSpeed = 2.5f;
static float deltaTime = 0.0f;
static float lastFrameTime = 0.0f;
static ImVec2 lastTouchPos = ImVec2(0, 0);

// Biến để theo dõi trạng thái cuộn và kéo
static bool isScrolling = false;

// Thêm các biến toàn cục để kiểm soát vị trí menu
static bool isDraggingTitleBar = false;
static ImVec2 menuDragOffset;
static ImVec2 menuPosition;
static bool menuPositionInitialized = false;




// Hàm tính toán khoảng cách di chuyển
float calculateMovement(float oldX, float oldY, float newX, float newY) {
    return sqrt(pow(newX - oldX, 2) + pow(newY - oldY, 2));
}

static size_t WriteCallback(void *ptr, size_t size, size_t nmemb, void *stream) {
    size_t written = fwrite(ptr, size, nmemb, (FILE *)stream);
    return written;
}

bool download_file(std::string url, std::string path) {
    // Tạo thư mục chứa file nếu chưa tồn tại
    std::string directory = path.substr(0, path.find_last_of('/'));
    mkdir(directory.c_str(), 0755);

    curl_global_init(CURL_GLOBAL_ALL);
    bool success = false;

    CURL *curl = curl_easy_init();
    if (curl) {
        FILE *file = fopen(path.c_str(), "wb");
        if (file) {
            curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
            curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
            curl_easy_setopt(curl, CURLOPT_DEFAULT_PROTOCOL, "https");
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, file);
            curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
            curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

            CURLcode result = curl_easy_perform(curl);
            fclose(file);

            success = (result == CURLE_OK);

            if (!success) {
                // Nếu tải thất bại, xóa file để không giữ file hỏng
                remove(path.c_str());
            }
        }
        curl_easy_cleanup(curl);
    }

    curl_global_cleanup();
    return success;
}

// Hàm kiểm tra anti-debugging
void AntiDebuggerCheck() {
    FILE *fp = fopen("/proc/self/status", "r");
    if (fp) {
        char line[256];
        while (fgets(line, sizeof(line), fp)) {
            if (strncmp(line, "TracerPid:", 10) == 0) {
                int tracer_pid = atoi(line + 10);
                if (tracer_pid != 0) {
                    exit(0);
                }
            }
        }
        fclose(fp);
    }
}



bool unZipIcon(std::string file_path) {
    // Kiểm tra xem file có tồn tại không
    struct stat file_stat;
    if (stat(file_path.c_str(), &file_stat) != 0) {
        return false;
    }

    // Tạo thư mục đích
    std::string directoryPath = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/").append("TH/");
    mkdir(directoryPath.c_str(), 0755);

    // Mở file zip
    int error_code = 0;
    zip *archive = zip_open(file_path.c_str(), 0, &error_code);
    if (!archive) {
        return false;
    }

    bool success = true;
    int num_entries = zip_get_num_entries(archive, 0);
    for (int i = 0; i < num_entries; ++i) {
        struct zip_stat file_info;
        if (zip_stat_index(archive, i, 0, &file_info) == 0) {
            // Bỏ qua thư mục
            if (file_info.name[strlen(file_info.name) - 1] == '/') {
                continue;
            }

            // Mở file trong archive
            zip_file *zip_f = zip_fopen_index(archive, i, 0);
            if (zip_f) {
                // Tạo đường dẫn cho file giải nén
                std::string out_path = directoryPath + file_info.name;

                // Tạo các thư mục con nếu cần
                std::string dir_path = out_path.substr(0, out_path.find_last_of('/'));
                for (size_t p = 0; p < dir_path.length(); p++) {
                    if (dir_path[p] == '/' && p > 0) {
                        std::string sub_path = dir_path.substr(0, p);
                        mkdir(sub_path.c_str(), 0755);
                    }
                }
                mkdir(dir_path.c_str(), 0755);

                // Mở file đích để ghi
                FILE *out_file = fopen(out_path.c_str(), "wb");
                if (out_file) {
                    // Cấp phát bộ nhớ cho buffer
                    char buffer[4096];
                    zip_int64_t bytes_read;

                    // Đọc và ghi dữ liệu
                    while ((bytes_read = zip_fread(zip_f, buffer, sizeof(buffer))) > 0) {
                        if (fwrite(buffer, 1, bytes_read, out_file) != (size_t)bytes_read) {
                            success = false;
                            break;
                        }
                    }

                    fclose(out_file);
                } else {
                    success = false;
                }

                zip_fclose(zip_f);
            } else {
                success = false;
            }
        }
    }

    zip_close(archive);

    // Xóa file zip sau khi giải nén thành công
    if (success) {
        if (remove(file_path.c_str()) != 0) {
            // Ghi log lỗi xóa file nếu cần
            // Nhưng vẫn trả về true vì giải nén đã thành công
        }
    }

    return success;
}
// Hàm lưu key vào bộ nhớ - Đã cập nhật với mã hóa và checksum
bool saveKeyToStorage(const char* key) {
    std::string filePath = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/").append("keydata.dat");
    FILE* file = fopen(filePath.c_str(), "wb");
    if (file) {
        // Tạo khóa mã hóa đơn giản từ tên gói
        std::string packageName = GetPack();
        char encryptionKey[16] = {0};
        for (size_t i = 0; i < std::min(packageName.length(), sizeof(encryptionKey)); i++) {
            encryptionKey[i] = packageName[i];
        }

        // Tính checksum
        uint32_t checksum = 0;
        for (size_t i = 0; i < strlen(key); i++) {
            checksum = checksum * 31 + key[i];
        }

        // Lưu checksum
        fwrite(&checksum, sizeof(checksum), 1, file);

        // Mã hóa và lưu key
        size_t keyLen = strlen(key);
        fwrite(&keyLen, sizeof(keyLen), 1, file);
        for (size_t i = 0; i < keyLen; i++) {
            char encryptedChar = key[i] ^ encryptionKey[i % sizeof(encryptionKey)];
            fwrite(&encryptedChar, 1, 1, file);
        }

        fclose(file);
        return true;
    }
    return false;
}

// Hàm đọc key từ bộ nhớ - Đã cập nhật với giải mã và kiểm tra checksum
bool loadKeyFromStorage(char* key, size_t maxSize) {
    std::string filePath = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/").append("keydata.dat");
    FILE* file = fopen(filePath.c_str(), "rb");
    if (file) {
        // Đọc checksum
        uint32_t storedChecksum;
        if (fread(&storedChecksum, sizeof(storedChecksum), 1, file) != 1) {
            fclose(file);
            return false;
        }

        // Đọc độ dài key
        size_t keyLen;
        if (fread(&keyLen, sizeof(keyLen), 1, file) != 1 || keyLen >= maxSize) {
            fclose(file);
            return false;
        }

        // Tạo lại khóa mã hóa
        std::string packageName = GetPack();
        char encryptionKey[16] = {0};
        for (size_t i = 0; i < std::min(packageName.length(), sizeof(encryptionKey)); i++) {
            encryptionKey[i] = packageName[i];
        }

        // Đọc và giải mã key
        for (size_t i = 0; i < keyLen; i++) {
            char encryptedChar;
            if (fread(&encryptedChar, 1, 1, file) != 1) {
                fclose(file);
                return false;
            }
            key[i] = encryptedChar ^ encryptionKey[i % sizeof(encryptionKey)];
        }
        key[keyLen] = '\0';

        // Kiểm tra checksum
        uint32_t calculatedChecksum = 0;
        for (size_t i = 0; i < keyLen; i++) {
            calculatedChecksum = calculatedChecksum * 31 + key[i];
        }

        if (calculatedChecksum != storedChecksum) {
            fclose(file);
            return false;
        }

        fclose(file);
        return true;
    }
    return false;
}

// Hàm lưu cấu hình map
bool SaveMapConfig() {
    // Tạo đường dẫn để lưu cấu hình
    std::string configPath = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/").append("map_config.dat");

    // Mở file để ghi
    FILE* file = fopen(configPath.c_str(), "wb");
    if (file) {
        // Ghi thông số Map Settings
        fwrite(&Config.ESPMenu.MapSize, sizeof(float), 1, file);
        fwrite(&Config.ESPMenu.IconSize, sizeof(float), 1, file);
        fwrite(&Config.ESPMenu.MapOffsetX, sizeof(float), 1, file);
        fwrite(&Config.ESPMenu.MapOffsetY, sizeof(float), 1, file);

        fclose(file);
        return true;
    }
    return false;
}

// Hàm để tải lại cấu hình map khi khởi động
bool LoadMapConfig() {
    // Đường dẫn file cấu hình
    std::string configPath = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/").append("map_config.dat");

    // Kiểm tra file có tồn tại không
    struct stat file_stat;
    if (stat(configPath.c_str(), &file_stat) == 0) {
        // Mở file để đọc
        FILE* file = fopen(configPath.c_str(), "rb");
        if (file) {
            // Đọc thông số Map Settings
            fread(&Config.ESPMenu.MapSize, sizeof(float), 1, file);
            fread(&Config.ESPMenu.IconSize, sizeof(float), 1, file);
            fread(&Config.ESPMenu.MapOffsetX, sizeof(float), 1, file);
            fread(&Config.ESPMenu.MapOffsetY, sizeof(float), 1, file);

            fclose(file);
            return true;
        }
    }
    return false;
}

// Hàm khởi tạo cấu hình ESP
void InitializeESPConfig() {
    // Cấu hình mặc định
    Config.ESPMenu.Enable_ESP = false;
    Config.ESPMenu.MinimapIcon = false;
    Config.ESPMenu.PlayerLine = false;
    Config.ESPMenu.PlayerBox = false;
    Config.ESPMenu.Icon = false;  // Thêm giá trị mặc định cho Icon

    // Cấu hình kích thước bản đồ
    Config.ESPMenu.MapSize = 410.0f;
    Config.ESPMenu.BigMapSize = 410.0f;
    Config.ESPMenu.MapOffsetX = 0.0f;
    Config.ESPMenu.MapOffsetY = 0.0f;
    Config.ESPMenu.IconSize = 18.0f;

    // Màu sắc mặc định
    Config.Color.enemy[0] = 1.0f;   // Red
    Config.Color.enemy[1] = 0.0f;   // Green
    Config.Color.enemy[2] = 0.0f;   // Blue
    Config.Color.enemy[3] = 1.0f;   // Alpha

    Config.Color.team[0] = 0.0f;    // Red
    Config.Color.team[1] = 1.0f;    // Green
    Config.Color.team[2] = 0.0f;    // Blue
    Config.Color.team[3] = 1.0f;    // Alpha

    Config.Color.line[0] = 1.0f;    // Red
    Config.Color.line[1] = 1.0f;    // Green
    Config.Color.line[2] = 1.0f;    // Blue
    Config.Color.line[3] = 0.8f;    // Alpha

    Config.Color.hp_high[0] = 0.0f; // Red
    Config.Color.hp_high[1] = 1.0f; // Green
    Config.Color.hp_high[2] = 0.0f; // Blue
    Config.Color.hp_high[3] = 1.0f; // Alpha

    Config.Color.hp_mid[0] = 1.0f;  // Red
    Config.Color.hp_mid[1] = 1.0f;  // Green
    Config.Color.hp_mid[2] = 0.0f;  // Blue
    Config.Color.hp_mid[3] = 1.0f;  // Alpha

    Config.Color.hp_low[0] = 1.0f;  // Red
    Config.Color.hp_low[1] = 0.0f;  // Green
    Config.Color.hp_low[2] = 0.0f;  // Blue
    Config.Color.hp_low[3] = 1.0f;  // Alpha

    // Cập nhật biến toàn cục để tương thích với code cũ
    Enable_ESP = Config.ESPMenu.Enable_ESP;
    MinimapIcon = Config.ESPMenu.MinimapIcon;
    PlayerLine = Config.ESPMenu.PlayerLine;
    PlayerBox = Config.ESPMenu.PlayerBox;
    Icon = Config.ESPMenu.Icon;

    // Khởi tạo đường dẫn icon
    imagePathBase = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/TH/");
}
#if defined(TOUCH_IL2CPP)

struct UnityEngine_Vector2_Fields {
    float x;
    float y;
};

struct UnityEngine_Vector2_o {
    UnityEngine_Vector2_Fields fields;
};

enum TouchPhase {
    Began = 0,
    Moved = 1,
    Stationary = 2,
    Ended = 3,
    Canceled = 4
};

struct UnityEngine_Touch_Fields {
    int32_t m_FingerId;
    struct UnityEngine_Vector2_o m_Position;
    struct UnityEngine_Vector2_o m_RawPosition;
    struct UnityEngine_Vector2_o m_PositionDelta;
    float m_TimeDelta;
    int32_t m_TapCount;
    int32_t m_Phase;
    int32_t m_Type;
    float m_Pressure;
    float m_maximumPossiblePressure;
    float m_Radius;
    float m_fRadiusVariance;
    float m_AltitudeAngle;
    float m_AzimuthAngle;
};

#endif
bool should_clear_mouse_pos = false;
EGLBoolean (*orig_eglSwapBuffers)(EGLDisplay dpy, EGLSurface surface);
EGLBoolean _eglSwapBuffers(EGLDisplay dpy, EGLSurface surface) {

    eglQuerySurface(dpy, surface, EGL_WIDTH, &glWidth);
    eglQuerySurface(dpy, surface, EGL_HEIGHT, &glHeight);

    if (glWidth <= 0 || glHeight <= 0) {
        return eglSwapBuffers(dpy, surface);
    }
    scaleX = (float) glWidth / screenWidth;
    scaleY = (float) glHeight / screenHeight;

    // Thêm kiểm tra xác thực định kỳ
    static uint32_t frameCount = 0;
    frameCount++;
    if (frameCount % 60 == 0) { // Kiểm tra mỗi 60 frame
        if (!checkAuthIntegrity()) {
            // Phát hiện dấu hiệu bất thường, đã reset auth
            showMenu = false;
        }
        AntiDebuggerCheck();
    }


    if (!setup){
        IMGUI_CHECKVERSION();
        ImGui::CreateContext();
        ImGuiIO& io = ImGui::GetIO();

        // Sử dụng giao diện tối như gốc
        ImGui::StyleColorsDark();

        // Tải font với kích thước lớn hơn để dễ nhìn
        io.Fonts->AddFontFromMemoryTTF(const_cast<std::uint8_t*>(Custom), sizeof(Custom), 30.f, NULL, io.Fonts->GetGlyphRangesVietnamese());
        ImGui_ImplOpenGL3_Init("#version 300 es");

        // Tăng tỷ lệ cho điều khiển cảm ứng tốt hơn
        ImGui::GetStyle().ScaleAllSizes(3.5f);

        // Tùy chỉnh phong cách UI
        ImGuiStyle& style = ImGui::GetStyle();
        style.WindowRounding = 10.0f;
        style.FrameRounding = 6.0f;
        style.ScrollbarRounding = 5.0f;
        style.TabRounding = 6.0f;
        style.GrabRounding = 6.0f;
        style.FramePadding = ImVec2(10, 6);
        style.ItemSpacing = ImVec2(10, 8);
        style.ItemInnerSpacing = ImVec2(8, 6);

        // Cấu hình hỗ trợ cảm ứng tốt hơn
        style.TouchExtraPadding = ImVec2(5.0f, 5.0f);
        style.ScrollbarSize = 12.0f;

        // Vô hiệu hóa thanh cuộn ngang tự động
        style.WindowMenuButtonPosition = ImGuiDir_None;

        // Set custom colors
        style.Colors[ImGuiCol_Button] = ImVec4(0.2f, 0.3f, 0.8f, 0.8f);
        style.Colors[ImGuiCol_ButtonHovered] = ImVec4(0.3f, 0.4f, 0.9f, 0.9f);
        style.Colors[ImGuiCol_ButtonActive] = ImVec4(0.4f, 0.5f, 1.0f, 1.0f);
        style.Colors[ImGuiCol_CheckMark] = ImVec4(0.0f, 0.8f, 0.3f, 1.0f);
        style.Colors[ImGuiCol_SliderGrab] = ImVec4(0.2f, 0.3f, 0.8f, 0.8f);
        style.Colors[ImGuiCol_SliderGrabActive] = ImVec4(0.4f, 0.5f, 1.0f, 1.0f);
        style.Colors[ImGuiCol_Header] = ImVec4(0.2f, 0.3f, 0.8f, 0.7f);
        style.Colors[ImGuiCol_HeaderHovered] = ImVec4(0.3f, 0.4f, 0.9f, 0.8f);
        style.Colors[ImGuiCol_HeaderActive] = ImVec4(0.4f, 0.5f, 1.0f, 1.0f);
        style.Colors[ImGuiCol_Tab] = ImVec4(0.15f, 0.20f, 0.45f, 0.8f);
        style.Colors[ImGuiCol_TabHovered] = ImVec4(0.25f, 0.30f, 0.60f, 0.9f);
        style.Colors[ImGuiCol_TabActive] = ImVec4(0.3f, 0.4f, 0.9f, 1.0f);

        // Điều chỉnh màu và kích thước thanh cuộn
        style.Colors[ImGuiCol_ScrollbarBg] = ImVec4(0.05f, 0.05f, 0.10f, 0.3f);
        style.Colors[ImGuiCol_ScrollbarGrab] = ImVec4(0.3f, 0.4f, 0.9f, 0.8f);
        style.Colors[ImGuiCol_ScrollbarGrabHovered] = ImVec4(0.4f, 0.5f, 1.0f, 0.9f);
        style.Colors[ImGuiCol_ScrollbarGrabActive] = ImVec4(0.5f, 0.6f, 1.0f, 1.0f);

        // Tải tài nguyên (giữ nguyên phần này)
        std::string file_path = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/").append("icon.zip");

        std::string urlIcon = std::string(OBFUSCATE("https://hmod.io.vn/Imgui-Connect/files/icon.zip"));

        imagePathBase = std::string("/storage/emulated/0/Android/data/").append(GetPack()).append("/files/TH/");

        if(!fileExists(file_path)){
            if(download_file(urlIcon, file_path)){
                unZipIcon(file_path);
            }
        }else{
            unZipIcon(file_path);
        }

        setup = true;
    }

    #if defined(TOUCH_IL2CPP)
    ImGuiIO* io = &ImGui::GetIO();
    int (*TouchCount)(void*) = (int (*)(void*)) (IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Input"), OBFUSCATE("get_touchCount"), 0));
    int touchCount = TouchCount(nullptr);

    // Tính deltaTime giữa các khung hình
    float currentTime = ImGui::GetTime();
    deltaTime = currentTime - lastFrameTime;
    lastFrameTime = currentTime;

    // Kiểm tra xem có đúng 3 ngón tay đang chạm màn hình không
    if (touchCount == 3) {
        // Lưu trữ vị trí các ngón tay
        float currentPositions[3][2];
        bool areFingersSteady = true;

        for (int i = 0; i < 3; i++) {
            UnityEngine_Touch_Fields touch = ((UnityEngine_Touch_Fields (*)(int)) (IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Input"), OBFUSCATE("GetTouch"), 1))) (i);

            currentPositions[i][0] = touch.m_Position.fields.x;
            currentPositions[i][1] = touch.m_Position.fields.y;

            // Nếu là lần chạm đầu tiên hoặc chưa phát hiện 3 ngón tay
            if (firstTouch || !threeFingerDetected) {
                lastTouchPositions[i][0] = currentPositions[i][0];
                lastTouchPositions[i][1] = currentPositions[i][1];
                firstTouch = false;
            } else {
                // Tính toán khoảng cách di chuyển
                float movement = calculateMovement(
                    lastTouchPositions[i][0], lastTouchPositions[i][1],
                    currentPositions[i][0], currentPositions[i][1]
                );

                // Nếu di chuyển vượt quá ngưỡng
                if (movement > MOVEMENT_THRESHOLD) {
                    areFingersSteady = false;
                    // Cập nhật vị trí mới
                    lastTouchPositions[i][0] = currentPositions[i][0];
                    lastTouchPositions[i][1] = currentPositions[i][1];
                }
            }
        }

        // Nếu chưa phát hiện 3 ngón tay, bắt đầu đếm thời gian
        if (!threeFingerDetected) {
            threeFingerDetected = true;
            threeFingerStartTime = std::chrono::steady_clock::now();
        } else if (areFingersSteady) {
            // Kiểm tra xem đã giữ 3 ngón tay đủ lâu chưa
            auto currentTime = std::chrono::steady_clock::now();
            float elapsedTime = std::chrono::duration<float>(currentTime - threeFingerStartTime).count();

            // Nếu đã giữ đủ thời gian và các ngón tay ổn định
            if (elapsedTime >= THREE_FINGER_DURATION) {
                showMenu = !showMenu;  // Chuyển đổi trạng thái menu (hiển thị/ẩn)
                threeFingerDetected = false;  // Reset trạng thái
                firstTouch = true; // Reset first touch flag

                std::this_thread::sleep_for(std::chrono::milliseconds(500));
            }
        } else {
            // Nếu ngón tay di chuyển, cập nhật lại thời gian bắt đầu
            threeFingerStartTime = std::chrono::steady_clock::now();
        }
    } else {
        // Reset trạng thái nếu không có đủ 3 ngón tay
        threeFingerDetected = false;
        firstTouch = true;
    }

    // Phương pháp triệt để xử lý cảm ứng
    if (touchCount > 0) {
        UnityEngine_Touch_Fields touch = ((UnityEngine_Touch_Fields (*)(int)) (IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Input"), OBFUSCATE("GetTouch"), 1))) (0);
        float reverseY = io->DisplaySize.y - touch.m_Position.fields.y;
        ImVec2 touchPos = ImVec2(touch.m_Position.fields.x, reverseY);

        // Luôn cập nhật vị trí chuột cho tương tác UI
        io->MousePos = touchPos;

        switch (touch.m_Phase) {
            case TouchPhase::Began:
            {
                io->MouseDown[0] = true;

                // Kiểm tra chạm vào thanh tiêu đề
                if (showMenu) {
                    float titleBarHeight = 40.0f;

                    if (touchPos.x >= menuPosition.x &&
                        touchPos.x <= menuPosition.x + 600 &&
                        touchPos.y >= menuPosition.y &&
                        touchPos.y <= menuPosition.y + titleBarHeight) {
                        isDraggingTitleBar = true;
                        menuDragOffset = ImVec2(touchPos.x - menuPosition.x, touchPos.y - menuPosition.y);
                    } else {
                        isDraggingTitleBar = false;
                    }
                }

                lastTouchPos = touchPos;
                break;
            }

            case TouchPhase::Moved:
            {
                if (isDraggingTitleBar) {
                    // Di chuyển menu theo cách thủ công
                    menuPosition.x = touchPos.x - menuDragOffset.x;
                    menuPosition.y = touchPos.y - menuDragOffset.y;

                    // Giới hạn vị trí trong màn hình
                    menuPosition.x = std::max(0.0f, std::min(menuPosition.x, glWidth - 600.0f));
                    menuPosition.y = std::max(0.0f, std::min(menuPosition.y, glHeight - 100.0f));
                } else {
                    // Xử lý cuộn
                    float deltaY = touchPos.y - lastTouchPos.y;
                    io->MouseWheel += deltaY * 0.01f;
                }

                lastTouchPos = touchPos;
                break;
            }

            case TouchPhase::Ended:
            case TouchPhase::Canceled:
            {
                io->MouseDown[0] = false;
                isDraggingTitleBar = false;
                should_clear_mouse_pos = true;
                break;
            }

            case TouchPhase::Stationary:
            {
                io->MouseDown[0] = true;
                break;
            }

            default:
                break;
        }
    } else {
        io->MouseDown[0] = false;
        isDraggingTitleBar = false;
    }

    #endif
    ImGui_ImplOpenGL3_NewFrame();
    ImGui_ImplAndroid_NewFrame(glWidth, glHeight);
    ImGui::NewFrame();

    // Vẽ ESP bất kể menu đang hiển thị hay không
    DrawESP(ImGui::GetForegroundDrawList());

    // Chỉ hiển thị menu khi biến showMenu = true
    if (showMenu) {
        // Khởi tạo vị trí menu nếu cần
                if (!menuPositionInitialized) {
            // Tính toán kích thước menu dựa trên tỷ lệ màn hình
            float menuWidth = glWidth * 0.5f;     // 3/5 chiều rộng màn hình
            float menuHeight = glHeight * 0.8f;   // 4/5 chiều cao màn hình

            // Vị trí để căn giữa màn hình
            menuPosition = ImVec2(
                (glWidth - menuWidth) / 2,        // Căn giữa theo chiều ngang
                (glHeight - menuHeight) / 2       // Căn giữa theo chiều dọc
            );

            menuPositionInitialized = true;
        }
        // Hiệu ứng chuyển màu RGB như cũ
        auto isFrames = ImGui::GetFrameCount();
        if(isFrames % 1 == 0) {
            if(isGreen == 0.01f && isBlue == 0.0f){
                isRed += 0.01f;
                 }

            if(isRed > 0.99f && isBlue == 0.0f){
                isRed = 1.0f;
                isGreen += 0.01f;
                }

            if(isGreen > 0.99f && isBlue == 0.0f){
                isGreen = 1.0f;
                isRed -= 0.01f;
            }

           if(isRed < 0.01f && isGreen == 1.0f){
                isRed = 0.0f;
                isBlue += 0.01f;
                }

            if(isBlue > 0.99f && isRed == 0.0f){
                isBlue = 1.0f;
                isGreen -= 0.01f;
                }
            if(isGreen < 0.01f && isBlue == 1.0f){
                isGreen = 0.0f;
                isRed += 0.01f;
                }
            if(isRed > 0.99f && isGreen == 0.0f){
                isRed = 1.0f;
                isBlue -= 0.01f;
                }
            if(isBlue < 0.01f && isGreen == 0.0f){
                isBlue = 0.0f;
                isRed -= 0.01f;
                if(isRed < 0.01f)
                isGreen = 0.01f;
            }
        }

        // Đặt vị trí menu tùy chỉnh
        ImGui::SetNextWindowPos(menuPosition, ImGuiCond_Always);
        ImGui::SetNextWindowSize(ImVec2(glWidth * 0.5f, glHeight * 0.8f), ImGuiCond_FirstUseEver);

       ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 3.0f);
        ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.07f, 0.07f, 0.12f, 0.95f));
        ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(isRed, isGreen, isBlue, 0.8f));

        // TẮT chức năng di chuyển của ImGui
        ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoMove;

        bool open = true;
        if (ImGui::Begin(OBFUSCATE("LIÊN QUÂN MOBILE 1.57.1.8 - HMOD.INFO"), &open, window_flags)) {

            // Gọi kiểm tra tính toàn vẹn định kỳ
            if (!checkAuthIntegrity()) {
                // Nếu phát hiện dấu hiệu bất thường, đã reset auth ở trên
                showMenu = false;
                ImGui::End();
                ImGui::PopStyleColor(2);
                ImGui::PopStyleVar();
                return orig_eglSwapBuffers(dpy, surface);
            }

            static bool LoginHack = false;
            static std::string err;

            if (!LoginHack) {
                ImGui::Text(OBFUSCATE(" Login! ( Copy Key Trước Khi Mở Game )"));

                // Khai báo biến để lưu key
                static char s[150] = {0};
                static bool keyLoaded = false;

                // Thử đọc key đã lưu khi lần đầu hiển thị màn hình đăng nhập
                if (!keyLoaded) {
                    keyLoaded = true;
                    if (loadKeyFromStorage(s, sizeof(s))) {
                        // Tự động đăng nhập với key đã lưu
                        err = Login(s);
                        if (err == "OK") {
                            LoginHack = isAuthenticated(); // Sử dụng hàm kiểm tra mới
                        }
                    }
                }

                ImGui::PushItemWidth(-1);
                ImGui::InputText("##key", s, sizeof s);

                ImGui::PopItemWidth();
                ImGui::PushItemWidth(-1);
                if (ImGui::Button(OBFUSCATE(" Paste Key  "), ImVec2(ImGui::GetWindowContentRegionWidth(), 0))) {
                    auto key = getClipboard();
                    strncpy(s, key.c_str(), sizeof s);
                }

                ImGui::PopItemWidth();

                ImGui::PushItemWidth(-1);

                if (ImGui::Button(OBFUSCATE("Login"), ImVec2(ImGui::GetWindowContentRegionWidth(), 0))) {
                    err = Login(s);
                    if (err == "OK") {
                        LoginHack = isAuthenticated(); // Sử dụng hàm kiểm tra mới

                        // Lưu key nếu đăng nhập thành công
                        if (isAuthenticated()) {
                            saveKeyToStorage(s);
                        }
                    }
                }

                ImGui::PopItemWidth();

                if (!err.empty() && err != std::string(OBFUSCATE("OK"))) {
                    ImGui::Text(OBFUSCATE("Lỗi : %s"), err.c_str());
                }

                // Hiển thị thông tin hết hạn nếu đăng nhập thành công
                if (isAuthenticated()) {
                    ImGui::TextColored(ImVec4(COL(0), COL(255), COL(0), COL(255)), OBFUSCATE("Ngày Hết Hạn: %s"), EXP.c_str());
                }
            } else {
                // Thêm kiểm tra xác thực định kỳ
                if (!isAuthenticated()) {
                    LoginHack = false;
                    showMenu = false;
                    ImGui::End();
                    ImGui::PopStyleColor(2);
                    ImGui::PopStyleVar();
                    return orig_eglSwapBuffers(dpy, surface);
                }

                ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]);
                ImGui::TextColored(ImVec4(isRed, isGreen, isBlue, 1.0f), OBFUSCATE("Arena of Valor Extrasensory Tool"));
                ImGui::PopFont();

                ImGui::Spacing();
                // Nice separator with gradient
                float window_width = ImGui::GetWindowWidth() - 20.0f;
                ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
                    ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
                    ImVec2(ImGui::GetCursorScreenPos().x + window_width, ImGui::GetCursorScreenPos().y + 4),
                    ImColor(isRed, isGreen, isBlue, 1.0f),
                    ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 1.0f),
                    ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 1.0f),
                    ImColor(isRed, isGreen, isBlue, 1.0f)
                );
                ImGui::Dummy(ImVec2(0, 8));

                // Kiểm tra token xác thực
                if (isAuthenticated()) {
                    // Thay đổi cách hiển thị TabBar để tốt hơn cho vuốt cuộn
                    ImGuiTabBarFlags tabbar_flags = ImGuiTabBarFlags_FittingPolicyScroll;
                    tabbar_flags |= ImGuiTabBarFlags_Reorderable;  // Cho phép sắp xếp lại các tab

                    if (ImGui::BeginTabBar("##tabbar", tabbar_flags)) {
                        // Kích thước tab lớn hơn cho dễ chạm
                        ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(15, 15));
                        ImGui::PushStyleColor(ImGuiCol_TabActive, ImVec4(0.3f, 0.4f, 0.9f, 1.0f));
                        ImGui::PushStyleColor(ImGuiCol_TabHovered, ImVec4(0.25f, 0.30f, 0.60f, 0.9f));
                        ImGui::PushStyleColor(ImGuiCol_Tab, ImVec4(0.15f, 0.20f, 0.45f, 0.8f));




 // Tab ESP Features kết hợp với các tính năng Hack
if (ImGui::BeginTabItem(OBFUSCATE("Main Menu"))) {
    ImGui::PopStyleVar();  // Pop FramePadding style

    // Lấy kích thước hiện có của khu vực nội dung
    float content_width = ImGui::GetContentRegionAvail().x;
    float available_height = ImGui::GetContentRegionAvail().y - 20; // Để lại chút khoảng trống dưới cùng

    // Tạo container chính cho toàn bộ nội dung của tab
    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width,
            ImGui::GetCursorScreenPos().y + available_height),
        ImColor(0.08f, 0.08f, 0.15f, 0.9f), 10.0f);

    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width,
            ImGui::GetCursorScreenPos().y + available_height),
        ImColor(isRed, isGreen, isBlue, 0.8f), 10.0f, 0, 2.0f);

    // Thêm padding bên trong container
    ImGui::Dummy(ImVec2(0, 8));
    ImGui::Indent(10);

    // Tạo khu vực cuộn cho nội dung tab
    ImGui::BeginChild("ESP_Features_ScrollRegion", ImVec2(content_width - 20, available_height - 20), false);

    ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(12, 12));
    ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0.0f, 0.8f, 0.3f, 1.0f));
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.1f, 0.1f, 0.2f, 0.8f));

    // Thêm viền xanh cho checkbox
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.0f, 0.5f, 1.0f, 1.0f)); // Màu xanh cho viền
    ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 2.0f); // Độ dày viền

    bool old_esp = Enable_ESP;
    ImGui::Checkbox(OBFUSCATE("Kích hoạt ESP"), &Config.ESPMenu.Enable_ESP);

    // Khôi phục style viền
    ImGui::PopStyleVar();
    ImGui::PopStyleColor();

    if (old_esp != Config.ESPMenu.Enable_ESP) {
        // Hiệu ứng nhấp nháy khi toggle
        ImGui::GetWindowDrawList()->AddRectFilled(
            ImVec2(ImGui::GetItemRectMin().x - 5, ImGui::GetItemRectMin().y - 5),
            ImVec2(ImGui::GetItemRectMax().x + 5, ImGui::GetItemRectMax().y + 5),
            ImColor(isRed, isGreen, isBlue, 0.5f),
            10.0f
        );
        Enable_ESP = Config.ESPMenu.Enable_ESP;
    }
    ImGui::PopStyleColor(2);
    ImGui::PopStyleVar();

    ImGui::Spacing();

    // Gradient separator
    float inner_content_width = ImGui::GetContentRegionAvail().x;
    ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + inner_content_width, ImGui::GetCursorScreenPos().y + 2),
        ImColor(isRed, isGreen, isBlue, 0.8f),
        ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 0.8f),
        ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 0.8f),
        ImColor(isRed, isGreen, isBlue, 0.8f)
    );
    ImGui::Dummy(ImVec2(0, 10));

    // ====== CAMERA HEIGHT CONTROL SECTION ======
    ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]);
    ImGui::TextColored(ImVec4(isRed, isGreen, isBlue, 1.0f), OBFUSCATE("Camera Height Control"));
    ImGui::PopFont();
    ImGui::Spacing();

    // Background cho Camera Control
    float camera_start_x = ImGui::GetCursorScreenPos().x;
    float camera_start_y = ImGui::GetCursorScreenPos().y;
    float camera_height = 80.0f;

    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(camera_start_x, camera_start_y),
        ImVec2(camera_start_x + inner_content_width, camera_start_y + camera_height),
        ImColor(0.1f, 0.1f, 0.2f, 0.4f), 8.0f
    );

    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(camera_start_x, camera_start_y),
        ImVec2(camera_start_x + inner_content_width, camera_start_y + camera_height),
        ImColor(isRed, isGreen, isBlue, 0.7f), 8.0f, 0, 2.0f
    );

    ImGui::Dummy(ImVec2(0, 10));

    // Style cho slider
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.1f, 0.1f, 0.2f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.15f, 0.15f, 0.25f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(0.2f, 0.2f, 0.3f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(isRed, isGreen, isBlue, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(isRed + 0.2f, isGreen + 0.2f, isBlue + 0.2f, 1.0f));

    ImGui::SliderInt("##DroneView", &CameraHeight, 0, 10, "Height: %d");

    // Nút điều chỉnh
    ImGui::SameLine();
    if (ImGui::Button("-", ImVec2(40, 40))) {
        if (CameraHeight > 0) CameraHeight--;
    }
    ImGui::SameLine();
    if (ImGui::Button("+", ImVec2(40, 40))) {
        if (CameraHeight < 10) CameraHeight++;
    }

    // Hiển thị giá trị hiện tại
    ImGui::SameLine();
    ImGui::BeginGroup();
    ImGui::EndGroup();

    ImGui::PopStyleColor(5);

    // Thêm separator sau phần Camera
    ImGui::Spacing();
    ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + inner_content_width, ImGui::GetCursorScreenPos().y + 2),
        ImColor(isRed, isGreen, isBlue, 0.8f),
        ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 0.8f),
        ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 0.8f),
        ImColor(isRed, isGreen, isBlue, 0.8f)
    );
    ImGui::Dummy(ImVec2(0, 10));

    // ====== FEATURES SECTION ======
    // Tính toán kích thước động cho hai cột
    float column_spacing = 30.0f;
    float column_width = (inner_content_width - column_spacing) / 2.0f;

    // Phần khung chứa các options - MỘT KHUNG DUY NHẤT BAO QUANH CẢ HAI CỘT
    ImGui::PushStyleVar(ImGuiStyleVar_FramePadding, ImVec2(10, 8));
    ImGui::PushStyleColor(ImGuiCol_CheckMark, ImVec4(0.0f, 0.8f, 0.3f, 1.0f));
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.1f, 0.1f, 0.2f, 0.6f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.15f, 0.15f, 0.25f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(0.2f, 0.2f, 0.3f, 0.8f));

    // Lưu vị trí bắt đầu của khu vực tính năng
    float features_start_x = ImGui::GetCursorScreenPos().x;
    float features_start_y = ImGui::GetCursorScreenPos().y;
    // Tăng chiều cao của khung để bao quanh đầy đủ các tùy chọn
    float features_height = 350.0f; // Tăng chiều cao để chứa thêm các tùy chọn mới

    // Vẽ khung nền và viền bao quanh cả hai cột tính năng
    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(features_start_x, features_start_y),
        ImVec2(features_start_x + inner_content_width, features_start_y + features_height),
        ImColor(0.1f, 0.1f, 0.2f, 0.4f), 8.0f
    );

    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(features_start_x, features_start_y),
        ImVec2(features_start_x + inner_content_width, features_start_y + features_height),
        ImColor(isRed, isGreen, isBlue, 0.7f), 8.0f, 0, 2.0f
    );


    // CỘT BÊN PHẢI - ESP FEATURES
    ImGui::BeginGroup();

    // Thêm padding bên trong
    ImGui::Dummy(ImVec2(0, 5));
    ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]);
    ImGui::TextColored(ImVec4(isRed, isGreen, isBlue, 1.0f), OBFUSCATE("ESP Features"));
    ImGui::PopFont();
    ImGui::Spacing();

    // Thêm viền xanh cho checkbox
    ImGui::PushStyleColor(ImGuiCol_Border, ImVec4(0.0f, 0.5f, 1.0f, 1.0f)); // Màu xanh cho viền
    ImGui::PushStyleVar(ImGuiStyleVar_FrameBorderSize, 2.0f); // Độ dày viền

    if (ImGui::Checkbox(OBFUSCATE(" Hack Mini Map        "), &Config.ESPMenu.MinimapIcon)) {
        MinimapIcon = Config.ESPMenu.MinimapIcon;
    }
    ImGui::SameLine();

    if (ImGui::Checkbox(OBFUSCATE(" Player Line"), &Config.ESPMenu.PlayerLine)) {
        PlayerLine = Config.ESPMenu.PlayerLine;
    }
    ImGui::Spacing();

    if (ImGui::Checkbox(OBFUSCATE(" Show Icon Map Lớn"), &Config.ESPMenu.Icon)) {
        Icon = Config.ESPMenu.Icon;
    }
    ImGui::SameLine();

    if (ImGui::Checkbox(OBFUSCATE(" Player Box"), &Config.ESPMenu.PlayerBox)) {
        PlayerBox = Config.ESPMenu.PlayerBox;
    }
    ImGui::Spacing();

    // Khôi phục style
    ImGui::PopStyleVar();
    ImGui::PopStyleColor();
    ImGui::EndGroup();

    ImGui::PopStyleColor(4);
    ImGui::PopStyleVar();

    ImGui::EndChild();  // End ScrollRegion
    ImGui::Unindent(10);
    ImGui::EndTabItem();
} else {
    ImGui::PopStyleVar();  // Pop FramePadding style if tab not selected
}
// Modify the Map Settings Tab to dynamically adjust the container height

if (ImGui::BeginTabItem(OBFUSCATE("Map Settings"))) {
    // Get available content space
    float content_height = ImGui::GetContentRegionAvail().y;
    float available_width = ImGui::GetContentRegionAvail().x;

    // Custom bordered container - use ImGui::GetContentRegionAvail().y instead of fixed height
    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + available_width,
            ImGui::GetCursorScreenPos().y + content_height - 10), // Subtract small margin
        ImColor(0.08f, 0.08f, 0.15f, 0.9f), 10.0f);

    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + available_width,
            ImGui::GetCursorScreenPos().y + content_height - 10), // Subtract small margin
        ImColor(isRed, isGreen, isBlue, 0.8f), 10.0f, 0, 2.0f);

    ImGui::Dummy(ImVec2(0, 8));
    ImGui::Indent(10);

    // Use full available height for scroll region, minus padding
    ImGui::BeginChild("Map_Settings_ScrollRegion", ImVec2(available_width - 20, content_height - 30), false);

    ImGui::Spacing();

    // Title with enhanced appearance
    ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]);
    ImGui::TextColored(ImVec4(isRed, isGreen, isBlue, 1.0f), OBFUSCATE("Map Settings"));
    ImGui::PopFont();

    ImGui::Spacing();

    // Gradient separator
    float content_width = ImGui::GetContentRegionAvail().x;
    ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + 2),
        ImColor(isRed, isGreen, isBlue, 0.8f),
        ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 0.8f),
        ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 0.8f),
        ImColor(isRed, isGreen, isBlue, 0.8f)
    );
    ImGui::Dummy(ImVec2(0, 8));

    // Background for Map Settings - make it adapt to content
    float map_settings_start_y = ImGui::GetCursorScreenPos().y;

    // Map settings controls
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.1f, 0.1f, 0.2f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.15f, 0.15f, 0.25f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(0.2f, 0.2f, 0.3f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(isRed, isGreen, isBlue, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(isRed + 0.2f, isGreen + 0.2f, isBlue + 0.2f, 1.0f));
    ImGui::PushStyleColor(ImGuiCol_Button, ImVec4(0.2f, 0.3f, 0.8f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_ButtonHovered, ImVec4(0.3f, 0.4f, 0.9f, 0.9f));
    ImGui::PushStyleColor(ImGuiCol_ButtonActive, ImVec4(0.4f, 0.5f, 1.0f, 1.0f));

    ImGui::Text(OBFUSCATE("Chỉnh lệch Mini Map(Điều chỉnh nếu bị lệch):"));
    ImGui::SliderFloat("##MiniMapScale", &Config.ESPMenu.MapSize, 100.0f, 800.0f, "%.0f");

    // Thêm nút cộng trừ cho Mini Map Scale (thay đổi 1 đơn vị)
    ImGui::SameLine();
    if (ImGui::Button(OBFUSCATE("-##MinusMapSize"), ImVec2(30, 0))) {
        Config.ESPMenu.MapSize = std::max(100.0f, Config.ESPMenu.MapSize - 1.0f);
    }
    ImGui::SameLine();
    if (ImGui::Button(OBFUSCATE("+##PlusMapSize"), ImVec2(30, 0))) {
        Config.ESPMenu.MapSize = std::min(800.0f, Config.ESPMenu.MapSize + 1.0f);
    }

    ImGui::Spacing();
    ImGui::Spacing();


    ImGui::Text(OBFUSCATE("Kích thước Icon (Điều chỉnh nếu Icon quá to hoặc nhỏ):"));
    ImGui::SliderFloat("##IconSize", &Config.ESPMenu.IconSize, 5.0f, 40.0f, "%.1f");

    // Thêm nút cộng trừ cho Icon Size (thay đổi 0.1 đơn vị)
    ImGui::SameLine();
    if (ImGui::Button(OBFUSCATE("-##MinusIconSize"), ImVec2(30, 0))) {
        Config.ESPMenu.IconSize = std::max(5.0f, Config.ESPMenu.IconSize - 1.0f);
    }
    ImGui::SameLine();
    if (ImGui::Button(OBFUSCATE("+##PlusIconSize"), ImVec2(30, 0))) {
        Config.ESPMenu.IconSize = std::min(40.0f, Config.ESPMenu.IconSize + 1.0f);
    }

    // Add map offset controls
    ImGui::Spacing();
    ImGui::Spacing();

    ImGui::Text(OBFUSCATE("Map Offset X:"));
    ImGui::SliderFloat("##MapOffsetX", &Config.ESPMenu.MapOffsetX, -200.0f, 200.0f, "%.1f");

    ImGui::SameLine();
    if (ImGui::Button(OBFUSCATE("-##MinusMapOffsetX"), ImVec2(30, 0))) {
        Config.ESPMenu.MapOffsetX = std::max(-200.0f, Config.ESPMenu.MapOffsetX - 1.0f);
    }
    ImGui::SameLine();
    if (ImGui::Button(OBFUSCATE("+##PlusMapOffsetX"), ImVec2(30, 0))) {
        Config.ESPMenu.MapOffsetX = std::min(200.0f, Config.ESPMenu.MapOffsetX + 1.0f);
    }

    ImGui::Spacing();
    ImGui::Spacing();

    ImGui::Text(OBFUSCATE("Map Offset Y:"));
    ImGui::SliderFloat("##MapOffsetY", &Config.ESPMenu.MapOffsetY, -200.0f, 200.0f, "%.1f");

    ImGui::SameLine();
    if (ImGui::Button(OBFUSCATE("-##MinusMapOffsetY"), ImVec2(30, 0))) {
        Config.ESPMenu.MapOffsetY = std::max(-200.0f, Config.ESPMenu.MapOffsetY - 1.0f);
    }
    ImGui::SameLine();
    if (ImGui::Button(OBFUSCATE("+##PlusMapOffsetY"), ImVec2(30, 0))) {
        Config.ESPMenu.MapOffsetY = std::min(200.0f, Config.ESPMenu.MapOffsetY + 1.0f);
    }

    ImGui::Spacing();
    ImGui::Spacing();
    ImGui::Spacing();

    // Now we know the height of all controls - draw the background with correct height
    float settings_height = ImGui::GetCursorScreenPos().y - map_settings_start_y + 50; // Add padding

    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(ImGui::GetCursorScreenPos().x - ImGui::GetStyle().ItemSpacing.x, map_settings_start_y - 5),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width + ImGui::GetStyle().ItemSpacing.x, map_settings_start_y + settings_height),
        ImColor(0.1f, 0.1f, 0.2f, 0.4f), 8.0f
    );

    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(ImGui::GetCursorScreenPos().x - ImGui::GetStyle().ItemSpacing.x, map_settings_start_y - 5),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width + ImGui::GetStyle().ItemSpacing.x, map_settings_start_y + settings_height),
        ImColor(isRed * 0.8f, isGreen * 0.8f, isBlue * 0.8f, 0.7f), 8.0f
    );

    // Thêm nút lưu cấu hình với hiệu ứng nổi bật
    float button_width = content_width * 0.8f; // Sử dụng 80% chiều rộng
    float button_x = ImGui::GetCursorScreenPos().x + (content_width - button_width) * 0.5f; // Căn giữa

    // Vẽ hiệu ứng nền gradient cho nút
    ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
        ImVec2(button_x - 5, ImGui::GetCursorScreenPos().y - 5),
        ImVec2(button_x + button_width + 5, ImGui::GetCursorScreenPos().y + 50),
        ImColor(isRed * 0.3f, isGreen * 0.3f, isBlue * 0.3f, 0.5f),
        ImColor(isRed * 0.4f, isGreen * 0.4f, isBlue * 0.4f, 0.5f),
        ImColor(isRed * 0.5f, isGreen * 0.5f, isBlue * 0.5f, 0.5f),
        ImColor(isRed * 0.3f, isGreen * 0.3f, isBlue * 0.3f, 0.5f)
    );

    // Biến tĩnh để theo dõi thông báo lưu cấu hình
    static bool showSaveMessage = false;
    static float saveMessageTimer = 0.0f;

    // Nút lưu lại cấu hình
    ImGui::SetCursorPosX((ImGui::GetWindowContentRegionWidth() - button_width) * 0.5f);
    if (ImGui::Button(OBFUSCATE("Lưu Cấu Hình"), ImVec2(button_width, 40))) {
        if (SaveMapConfig()) {
            showSaveMessage = true;
            saveMessageTimer = 2.0f; // Hiển thị thông báo trong 2 giây
        }
    }

    // Hiển thị thông báo khi lưu thành công
    if (showSaveMessage) {
        ImGui::Spacing();
        ImGui::TextColored(ImVec4(0.0f, 1.0f, 0.0f, 1.0f), OBFUSCATE("Cấu hình đã được lưu thành công!"));
        saveMessageTimer -= ImGui::GetIO().DeltaTime;
        if (saveMessageTimer <= 0.0f) {
            showSaveMessage = false;
        }
    }

    ImGui::PopStyleColor(8);

    ImGui::EndChild();
    ImGui::Unindent(10);
    ImGui::EndTabItem();
}

// Menu Information Tab
if (ImGui::BeginTabItem(OBFUSCATE("Menu Information"))) {
    // Custom bordered container
    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + ImGui::GetContentRegionAvail().x,
            ImGui::GetCursorScreenPos().y + 420),
        ImColor(0.08f, 0.08f, 0.15f, 0.9f), 10.0f);

    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + ImGui::GetContentRegionAvail().x,
            ImGui::GetCursorScreenPos().y + 420),
        ImColor(isRed, isGreen, isBlue, 0.8f), 10.0f, 0, 2.0f);

    ImGui::Dummy(ImVec2(0, 8));
    ImGui::Indent(10);

    ImGui::BeginChild("Information_ScrollRegion", ImVec2(ImGui::GetContentRegionAvail().x - 20, 400), false);

    // Add fancy background for information
    float content_width = ImGui::GetContentRegionAvail().x;
    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(ImGui::GetCursorScreenPos().x - 5, ImGui::GetCursorScreenPos().y - 5),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + 100),
        ImColor(0.1f, 0.1f, 0.2f, 0.4f), 8.0f
    );

    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(ImGui::GetCursorScreenPos().x - 5, ImGui::GetCursorScreenPos().y - 5),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + 100),
        ImColor(isRed * 0.8f, isGreen * 0.8f, isBlue * 0.8f, 0.7f), 8.0f
    );

    ImGui::Spacing();
    ImGui::Spacing();

    // Info title
    ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]);
    ImGui::TextColored(ImVec4(isRed, isGreen, isBlue, 1.0f), OBFUSCATE("Thông Tin Tài Khoản"));
    ImGui::PopFont();

    ImGui::Spacing();

    // Thông tin hết hạn với style đẹp
    ImGui::TextColored(ImVec4(COL(0), COL(255), COL(0), COL(255)), OBFUSCATE("Ngày Hết Hạn: %s"), EXP.c_str());

    ImGui::Spacing();

    // Trạng thái antiban
    if (!Bypass) {
        ImGui::TextColored(ImVec4(COL(255), COL(0), COL(0), COL(255)), "Antiban Status: Deactivated");
    } else {
        ImGui::TextColored(ImVec4(COL(0), COL(255), COL(0), COL(255)), "Antiban Status: Activated");
    }

    ImGui::Spacing();
    ImGui::Spacing();

    // Thông tin người bán
    ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]);
    ImGui::TextColored(ImVec4(isRed, isGreen, isBlue, 1.0f), OBFUSCATE("Thông Tin Người Bán"));
    ImGui::PopFont();

    ImGui::Spacing();

    // Hiển thị thông tin người bán (nếu có)
    if (hovaten.length() > 0) {
        ImGui::TextColored(ImVec4(1.0f, 1.0f, 1.0f, 1.0f), OBFUSCATE("Tên Seller : %s"), hovaten.c_str());
    }

    ImGui::Spacing();
    ImGui::Spacing();

    // Thông tin phiên bản
    ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]);
    ImGui::TextColored(ImVec4(isRed, isGreen, isBlue, 1.0f), OBFUSCATE("Thông Tin Phiên Bản"));
    ImGui::PopFont();

    ImGui::Spacing();

    // Hiển thị thông báo phiên bản (nếu có)
    if (thongbao.length() > 0) {
        ImGui::TextColored(ImVec4(1.0f, 1.0f, 1.0f, 1.0f), OBFUSCATE("%s"), thongbao.c_str());
    } else {
        ImGui::TextColored(ImVec4(1.0f, 1.0f, 1.0f, 1.0f), OBFUSCATE("Phiên bản: Không xác định "));
    }

    ImGui::Spacing();
    ImGui::Spacing();

    // Kiểm tra hạn sử dụng thông qua hệ thống xác thực mới
    if (!isAuthenticated() || time(0) > g_AuthExpireTime) {
        resetAuth();
        LoginHack = false;
        ImGui_ImplAndroid_Shutdown();
        ImGui_ImplOpenGL3_Shutdown();
        ImGui::DestroyContext();
    }

    ImGui::EndChild();
    ImGui::Unindent(10);
    ImGui::EndTabItem();
}
// Settings Tab
if (ImGui::BeginTabItem(OBFUSCATE("Settings"))) {
    // Custom bordered container
    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + ImGui::GetContentRegionAvail().x,
            ImGui::GetCursorScreenPos().y + 420),
        ImColor(0.08f, 0.08f, 0.15f, 0.9f), 10.0f);

    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + ImGui::GetContentRegionAvail().x,
            ImGui::GetCursorScreenPos().y + 420),
        ImColor(isRed, isGreen, isBlue, 0.8f), 10.0f, 0, 2.0f);

    ImGui::Dummy(ImVec2(0, 8));
    ImGui::Indent(10);

    ImGui::BeginChild("Settings_ScrollRegion", ImVec2(ImGui::GetContentRegionAvail().x - 20, 400), false);

    float content_width = ImGui::GetContentRegionAvail().x;

    // Thông tin về thao tác cử chỉ
    ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]);
    ImGui::TextColored(ImVec4(isRed, isGreen, isBlue, 1.0f), OBFUSCATE("Menu Trigger:"));
    ImGui::PopFont();

    // Gradient separator
    ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + 2),
        ImColor(isRed, isGreen, isBlue, 0.8f),
        ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 0.8f),
        ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 0.8f),
        ImColor(isRed, isGreen, isBlue, 0.8f)
    );
    ImGui::Dummy(ImVec2(0, 8));

    // Background for trigger settings
    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(ImGui::GetCursorScreenPos().x - 5, ImGui::GetCursorScreenPos().y - 5),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + 200),
        ImColor(0.1f, 0.1f, 0.2f, 0.4f), 8.0f
    );

    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(ImGui::GetCursorScreenPos().x - 5, ImGui::GetCursorScreenPos().y - 5),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + 200),
        ImColor(isRed * 0.8f, isGreen * 0.8f, isBlue * 0.8f, 0.7f), 8.0f
    );

    ImGui::Dummy(ImVec2(0, 5));

    // Hiển thị trạng thái hiện tại của menu
    ImGui::Text(OBFUSCATE("Menu Status: %s"), showMenu ? OBFUSCATE("Visible") : OBFUSCATE("Hidden"));

    ImGui::Spacing();

    // Hướng dẫn sử dụng
    ImGui::TextWrapped(OBFUSCATE("Để hiển thị/ẩn menu, đặt 3 ngón tay trên màn hình và giữ cố định trong 1.5 giây."));

    ImGui::Spacing();
    ImGui::Spacing();

    // Tùy chỉnh thời gian giữ và độ nhạy
    ImGui::PushStyleColor(ImGuiCol_FrameBg, ImVec4(0.1f, 0.1f, 0.2f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgHovered, ImVec4(0.15f, 0.15f, 0.25f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_FrameBgActive, ImVec4(0.2f, 0.2f, 0.3f, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrab, ImVec4(isRed, isGreen, isBlue, 0.8f));
    ImGui::PushStyleColor(ImGuiCol_SliderGrabActive, ImVec4(isRed + 0.2f, isGreen + 0.2f, isBlue + 0.2f, 1.0f));

    ImGui::Text(OBFUSCATE("Hold Duration (seconds):"));
    ImGui::SliderFloat("##hold_duration", &userHoldDuration, 0.5f, 3.0f, "%.1f");

    // Update the actual hold duration
    THREE_FINGER_DURATION = userHoldDuration;

    ImGui::Spacing();
    ImGui::Spacing();

    ImGui::Text(OBFUSCATE("Touch Sensitivity:"));
    ImGui::SliderFloat("##movement_threshold", &MOVEMENT_THRESHOLD, 5.0f, 30.0f, "%.1f");
    ImGui::Text(OBFUSCATE("Lower = More sensitive, Higher = More stable"));

    ImGui::PopStyleColor(5);

    ImGui::EndChild();
    ImGui::Unindent(10);
    ImGui::EndTabItem();
}
/*
// ESP Colors Tab
if (ImGui::BeginTabItem(OBFUSCATE("ESP Colors"))) {
    // Custom bordered container
    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + ImGui::GetContentRegionAvail().x,
            ImGui::GetCursorScreenPos().y + 420),
        ImColor(0.08f, 0.08f, 0.15f, 0.9f), 10.0f);

    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + ImGui::GetContentRegionAvail().x,
            ImGui::GetCursorScreenPos().y + 420),
        ImColor(isRed, isGreen, isBlue, 0.8f), 10.0f, 0, 2.0f);

    ImGui::Dummy(ImVec2(0, 8));
    ImGui::Indent(10);

    ImGui::BeginChild("Colors_ScrollRegion", ImVec2(ImGui::GetContentRegionAvail().x - 20, 400), false);

    float content_width = ImGui::GetContentRegionAvail().x;

    ImGui::PushFont(ImGui::GetIO().Fonts->Fonts[0]);
    ImGui::TextColored(ImVec4(isRed, isGreen, isBlue, 1.0f), OBFUSCATE("ESP Color Settings:"));
    ImGui::PopFont();

    // Gradient separator
    ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + 2),
        ImColor(isRed, isGreen, isBlue, 0.8f),
        ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 0.8f),
        ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 0.8f),
        ImColor(isRed, isGreen, isBlue, 0.8f)
    );
    ImGui::Dummy(ImVec2(0, 8));

    // Background for main colors
    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(ImGui::GetCursorScreenPos().x - 5, ImGui::GetCursorScreenPos().y - 5),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + 140),
        ImColor(0.1f, 0.1f, 0.2f, 0.4f), 8.0f
    );

    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(ImGui::GetCursorScreenPos().x - 5, ImGui::GetCursorScreenPos().y - 5),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + 140),
        ImColor(isRed * 0.8f, isGreen * 0.8f, isBlue * 0.8f, 0.7f), 8.0f
    );

    ImGui::Dummy(ImVec2(0, 5));

    // Line Color
    ImGui::Text(OBFUSCATE("Line Color:"));
    ImGui::ColorEdit4("##LineColor", Config.Color.line, ImGuiColorEditFlags_AlphaBar);

    ImGui::Spacing();

    // Enemy Color
    ImGui::Text(OBFUSCATE("Enemy Color:"));
    ImGui::ColorEdit4("##EnemyColor", Config.Color.enemy, ImGuiColorEditFlags_AlphaBar);

    ImGui::Spacing();

    // Team Color
    ImGui::Text(OBFUSCATE("Team Color:"));
    ImGui::ColorEdit4("##TeamColor", Config.Color.team, ImGuiColorEditFlags_AlphaBar);

    ImGui::Spacing();

    // Health Bar Colors Section
    ImGui::GetWindowDrawList()->AddRectFilledMultiColor(
        ImVec2(ImGui::GetCursorScreenPos().x, ImGui::GetCursorScreenPos().y),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + 2),
        ImColor(isRed, isGreen, isBlue, 0.8f),
        ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 0.8f),
        ImColor(1.0f - isRed, 1.0f - isGreen, 1.0f - isBlue, 0.8f),
        ImColor(isRed, isGreen, isBlue, 0.8f)
    );
    ImGui::Dummy(ImVec2(0, 8));

    ImGui::Text(OBFUSCATE("Health Bar Colors:"));

    // Background for health colors
    ImGui::GetWindowDrawList()->AddRectFilled(
        ImVec2(ImGui::GetCursorScreenPos().x - 5, ImGui::GetCursorScreenPos().y - 5),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + 140),
        ImColor(0.1f, 0.1f, 0.2f, 0.4f), 8.0f
    );

    ImGui::GetWindowDrawList()->AddRect(
        ImVec2(ImGui::GetCursorScreenPos().x - 5, ImGui::GetCursorScreenPos().y - 5),
        ImVec2(ImGui::GetCursorScreenPos().x + content_width, ImGui::GetCursorScreenPos().y + 140),
        ImColor(isRed * 0.8f, isGreen * 0.8f, isBlue * 0.8f, 0.7f), 8.0f
    );

    ImGui::Dummy(ImVec2(0, 5));

    // High Health Color
    ImGui::Text(OBFUSCATE("High Health (>60%):"));
    ImGui::ColorEdit4("##HighHealthColor", Config.Color.hp_high, ImGuiColorEditFlags_AlphaBar);

    ImGui::Spacing();

    // Medium Health Color
    ImGui::Text(OBFUSCATE("Medium Health (30-60%):"));
    ImGui::ColorEdit4("##MediumHealthColor", Config.Color.hp_mid, ImGuiColorEditFlags_AlphaBar);

    ImGui::Spacing();

    // Low Health Color
    ImGui::Text(OBFUSCATE("Low Health (<30%):"));
    ImGui::ColorEdit4("##LowHealthColor", Config.Color.hp_low, ImGuiColorEditFlags_AlphaBar);

    ImGui::EndChild();
    ImGui::Unindent(10);
    ImGui::EndTabItem();
}
*/
                        ImGui::PopStyleColor(3); // Pop tab colors
                        ImGui::EndTabBar();
                    }
                }
            }
        }

        // Xử lý khi nhấn nút đóng (dấu X)
        if (!open) {
            showMenu = false; // Ẩn hoàn toàn menu thay vì chỉ thu gọn
        }

        ImGui::End();
        ImGui::PopStyleColor(2);
        ImGui::PopStyleVar();
    }

    ImGui::Render();
    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

    if (should_clear_mouse_pos) {
        ImGui::GetIO().MousePos = ImVec2(-1, -1);
        should_clear_mouse_pos = false;
    }

    return orig_eglSwapBuffers(dpy, surface);
}

void *Init_Thread(void *) {
    AntiDebuggerCheck();
   espManager = new ESPManager();
   ActorLinker_enemy = new ESPManager();
   pthread_t antiDebugThread;
   pthread_create(&antiDebugThread, NULL, [](void*) -> void* {
       while (true) {
           AntiDebuggerCheck();
           sleep(5); // Kiểm tra mỗi 5 giây
       }
       return nullptr;
   }, NULL);

   InitializeESPConfig(); // Khởi tạo cấu hình ESP
   LoadMapConfig();
   KittyMemory::ProcMap Il2cppMap, AnogsMap;
   while (!Il2cppMap.isValid() && !AnogsMap.isValid()) {
       Il2cppMap = KittyMemory::getLibraryMap("libil2cpp.so");
       AnogsMap = KittyMemory::getLibraryMap("libanogs.so");
       Bypass = true;
       sleep(1);
   }
   IL2Cpp::Il2CppAttach();

Tools::Hook((void *) DobbySymbolResolver(OBFUSCATE("/system/lib/libEGL.so"), OBFUSCATE("eglSwapBuffers")), (void *) _eglSwapBuffers, (void **) &orig_eglSwapBuffers);
#if defined(__aarch64__)
MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CLoginSystem"), OBFUSCATE("_LoginSuccess"), 1),"C0035FD6").Modify();

 MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CGCloudUpdateSystem"), OBFUSCATE("get_IsAutoLogin"), 0),"C0035FD6").Modify();

#else
MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CLoginSystem"), OBFUSCATE("_LoginSuccess"), 1),"1EFF2FE1").Modify();

 MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CGCloudUpdateSystem"), OBFUSCATE("get_IsAutoLogin"), 0),"1EFF2FE1").Modify();

#endif

while (!isAuthenticated()) {
        sleep(1);
    }

    // Sau khi đăng nhập thành công thì mới áp dụng các hook khác
    if (isAuthenticated()) {

#include "Hooks.h"
  }
    return nullptr;
}



JNIEXPORT jint JNICALL
JNI_OnLoad(JavaVM *vm, void * reserved) {
    jvm = vm;
    JNIEnv *env;

    AntiDebuggerCheck();
    vm->GetEnv((void **) &env, JNI_VERSION_1_6);

    Tools::Hook((void *) DobbySymbolResolver(OBFUSCATE("/system/lib/libandroid.so"), OBFUSCATE("ANativeWindow_getWidth")), (void *) _ANativeWindow_getWidth, (void **) &orig_ANativeWindow_getWidth);
    Tools::Hook((void *) DobbySymbolResolver(OBFUSCATE("/system/lib/libandroid.so"), OBFUSCATE("ANativeWindow_getHeight")), (void *) _ANativeWindow_getHeight, (void **) &orig_ANativeWindow_getHeight);

    pthread_t myThread;
    pthread_create(&myThread, NULL, Init_Thread, NULL);

    return JNI_VERSION_1_6;
}


